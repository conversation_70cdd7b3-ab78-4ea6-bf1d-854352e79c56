# Unified NCA Viewer

This project provides a unified interface for visualizing both NoiseNCA and VNCA (Variational Neural Cellular Automata) models. It allows switching between the two models and loading different weights for each.

## Features

- Displays the NCA evolution in real-time
- Shows the target image for reference
- Provides controls for adjusting the speed of evolution
- Allows resetting to the initial state
- Counts the number of steps (epochs) taken

- Allows switching between NoiseNCA and VNCA models
- Includes a circular noise brush for interactive experimentation

## Files

- `run_unified_nca.py` - Main script for running both models
- `nca_base.py` - Base class for NCA models
- `noise_nca.py` - NoiseNCA implementation
- `vnca.py` - VNCA implementation
- `nca_ui.py` - UI for visualizing NCA models
- `models.py` - Model definitions
- `Noise-NCA.yml` - Configuration for NoiseNCA

## Controls

- **Start/Stop button**: Start or stop the animation
- **Reset to Noise button**: Reset to the initial state
- **Load Image button**: Reload the target image

- **Switch Model button**: Switch between NoiseNCA and VNCA models
- **Noise Brush button**: Toggle the brush tool for applying noise
- **Space**: Start/Stop the animation (keyboard shortcut)
- **R**: Reset to the initial state (keyboard shortcut)
- **B**: Toggle the brush tool (keyboard shortcut)
- **Update Interval slider**: Control the time between updates
- **Steps Per Update slider**: Control how many steps to take per update
- **Brush Radius slider**: Control the size of the noise brush

## Usage

Run the unified NCA viewer:

```
python run_unified_nca.py
```

## Brush Tool

The brush tool allows you to apply noise to specific areas of the NCA state:

1. Click the "Noise Brush" button or press 'B' to activate
2. Adjust the brush size using the slider
3. Click or drag on the image to apply noise
4. Watch how the NCA model evolves from the disturbed state

The brush applies the same type of noise that is used when resetting the model, creating a consistent experience.

## Model Weights

- NoiseNCA: `results/NoiseNCA/train/final_model.pth`
- VNCA: `results/NoiseNCA/final_model_VAE.pth`



## Project Structure

.
├── Noise-NCA.yml              # Configuration for NoiseNCA
├── README_unified_nca.md      # New README for the unified implementation
├── another_NCA/               # VNCA model code
├── backup/                    # Backup of intermediate development files
├── data_test/                 # Test data
├── data_train/                # Training data
├── models.py                  # Model definitions
├── nca_base.py                # Base class for NCA models
├── nca_ui.py                  # UI for visualizing NCA models
├── noise_nca.py               # NoiseNCA implementation
├── requirements.txt           # Project dependencies
├── results/                   # Model weights
├── run_unified_nca.py         # Main script for running both models
├── states/                    # Saved states
└── vnca.py                    # VNCA implementation
