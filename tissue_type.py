

def extract_tissue_info(filename):
    data = []

    with open(filename, 'r') as file:
        next(file)
        
        for line in file:
            parts = [part.strip() for part in line.strip().split(',')]
            
            dataset_name = parts[0]
            adata_filename = parts[1]
            unique_adata_filename = parts[2]
            general_tissue = parts[3]
            specific_tissue_region = parts[4]
            cancer_status = parts[5]
        
            data.append({
                'dataset_name': dataset_name,
                'adata_filename': adata_filename,
                'unique_adata_filename': unique_adata_filename,
                'general_tissue': general_tissue,
                'specific_tissue_region': specific_tissue_region,
                'cancer_status': cancer_status
            })

    return data


filename = 'tissue_annotation_human.csv'
tissue_type = extract_tissue_info(filename)
import pdb; pdb.set_trace()
print("Extracted Data:", tissue_type)

'''
(Pdb) tissue_type[1]
{'dataset_name': '', 'adata_filename': 'Pla_HDBR9518710', 'unique_adata_filename': 'data1_decidua_basalis_8sample_Pla_HDBR9518710', 'general_tissue': 'uterus', 'specific_tissue_region': 'decidua basalis', 'cancer_status': 'Normal'}

(Pdb) tissue_type['adata_filename'=='Pla_Camb9518737']
{'dataset_name': 'data1_decidua basalis_8sample', 'adata_filename': 'Pla_Camb9518737', 'unique_adata_filename': 'data1_decidua_basalis_8sample_Pla_Camb9518737', 'general_tissue': 'uterus', 'specific_tissue_region': 'decidua basalis', 'cancer_status': 'Normal'}

(Pdb) tissue_type['adata_filename'=='Pla_Camb9518737']['general_tissue']
'uterus'

'''