# Copyright (c) 2022 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# print('dilated_attention.py')
import math
import os
from datetime import datetime
import torch
import torch.nn.functional as F
from einops import rearrange
from datetime import datetime

from .multihead_attention import MultiheadAttention
from .utils import padding_to_multiple_of, all_gather_func, get_data_parallel_rank, get_data_parallel_world_size
from .util_plot import plot_attention_heatmaps_onerow, plot_attention_heatmaps, plot_qkvi_heatmaps

class DilatedAttention(MultiheadAttention):

    def dense_to_sparse(self, x, ratio):
        # print('== dense_to_sparse ==')
        # print('ratio',ratio)
        # print('x.shape',x.shape)
        length = x.size(1)
        padding = padding_to_multiple_of(length, ratio)
        head_padding = padding_to_multiple_of(self.num_heads, ratio)

        if padding > 0 or head_padding > 0:
            x = F.pad(x, (0, 0, 0, head_padding, 0, padding), value = 0.)

        x = rearrange(x, 'b (l r1) (r2 h) d -> b l h d r1 r2', r1=ratio, r2=ratio)
        x = torch.diagonal(x, offset=0, dim1=4, dim2=5)
        x = rearrange(x, 'b l h d r -> b l (r h) d')
        
        if head_padding > 0:
            x = x[:, :, :self.num_heads]

        return x

    def sparse_to_dense(self, out, lse, ratio):
        head_padding = padding_to_multiple_of(self.num_heads, ratio)

        if head_padding > 0:
            out = F.pad(out, (0, 0, 0, head_padding), value = 0.)
            lse = F.pad(lse, (0, 0, 0, head_padding), value = -1e8)

        out = rearrange(out, 'b l (r h) d -> b l h d r', r=ratio)
        out = torch.diag_embed(out, offset=0, dim1=4, dim2=5)
        out = rearrange(out, 'b l h d r1 r2 -> b (r2 h) (l r1) d', r1=ratio, r2=ratio)

        lse = rearrange(lse, 'b (r h) l -> b l h r', r=ratio)
        lse = torch.diag_embed(lse, offset=0, dim1=3, dim2=4)
        lse = lse.masked_fill_(lse==0, -1e8)
        lse = rearrange(lse, 'b l h r1 r2 -> b (r2 h) (l r1) 1', r1=ratio, r2=ratio)

        if head_padding > 0:
            out = out[:, :self.num_heads]
            lse = lse[:, :self.num_heads]

        return out, lse

    def gather_kv(self, x, sl, seq_len, is_causal=True):
        bsz = x.size(0)
        assert sl % seq_len == 0
        num_rank_per_segment = sl // seq_len

        x = all_gather_func(x)
        current_rank = get_data_parallel_rank()
        x = rearrange(x, '(w b) l h d -> w b l h d', b=bsz)
        
        if is_causal:
            if current_rank > 0:
                x = x[:current_rank]
            else:
                x = x[:1] * 0
        
        current_segment = current_rank // num_rank_per_segment * num_rank_per_segment
        x = x[current_segment:current_segment+num_rank_per_segment]

        x = rearrange(x, 'w b l h d -> b (w l) h d')
        return x
    
    def gathering(self, x, dr, sl, is_causal=True, offset=0, is_kv=False, seq_parall=True):

        curr_x = x
        if offset > 0:
            # print('offset > 0')
            curr_x = F.pad(curr_x, (0, 0, 0, 0, offset % sl, 0), value=0.)
            # print('curr_x',curr_x.shape)
        seq_len = curr_x.size(1)
        # print('seq_len',seq_len)
        should_gather_kv = is_kv and (get_data_parallel_world_size() > 1) and (sl > seq_len) and seq_parall
        # print('should_gather_kv',should_gather_kv)
        _sl = sl
        # print('_sl',_sl)
        sl = min(sl, seq_len)
        # print('sl',sl)
        padding = padding_to_multiple_of(seq_len, sl)
        # print('padding',padding)

        if padding > 0:
            curr_x = F.pad(curr_x, (0, 0, 0, 0, 0, padding), value = 0.)
            # print('curr_x.shape',curr_x.shape)

        curr_x = rearrange(curr_x, 'b (n g) h d -> (b n) g h d', g=sl)
        # print('rearrange curr_x.shape',curr_x.shape)
        curr_x = self.dense_to_sparse(curr_x, dr)
        # print('dense_to_sparse curr_x.shape',curr_x.shape)

        if should_gather_kv:
            curr_x = self.gather_kv(curr_x, _sl, seq_len, is_causal)
            # print('gather_kv curr_x.shape',curr_x.shape)

        curr_x = rearrange(curr_x, 'b l h d -> (b h) l d')
        # print('rearrange 2 curr_x.shape',curr_x.shape)
        
        return curr_x

    def scattering(self, outs, lses, seq_len, bsz, offset=0,type_label='flash'):
        assert len(outs) == len(lses)

        if type_label=='flash':
            assert len(outs) % len(self.args.dilated_ratio) == 0
            drs = self.args.dilated_ratio
        elif type_label=='standard':
            assert len(outs) % 1 == 0
            drs = [1]
            
        all_outs, all_lses = [], []
        if len(outs) > len(drs):
            drs = drs * (len(outs) // len(drs))

        for dr, o, lse in zip(drs, outs, lses):
            o = rearrange(o, 'b l (h d) -> b l h d', h=self.num_heads)
            o, lse = self.sparse_to_dense(o, lse, dr)
            o = rearrange(o, '(b n) h g d -> (b h) (n g) d', b=bsz)
            lse = rearrange(lse, '(b n) h g 1 -> (b h) (n g) 1', b=bsz)
            o = o[:, offset:offset+seq_len]
            lse = lse[:, offset:offset+seq_len]

            all_outs.append(o)
            all_lses.append(lse)

        with torch.no_grad():
            max_lse = torch.stack(all_lses, dim=0)
            max_lse = max_lse.max(0)[0]
            all_lses = [torch.exp(lse-max_lse) for lse in all_lses]
            lse_sum = torch.stack(all_lses, dim=0).sum(0)
            all_lses = [lse / lse_sum for lse in all_lses]

        out = 0
        for o, lse in zip(all_outs, all_lses):
            out += o * lse.type_as(o)
        out = rearrange(out, '(b h) l d -> b l (h d)', h=self.num_heads)

        return out

    def forward(
        self,
        query,
        key,
        value,
        query_cls,
        layer_n,
        this_head=None,
        use_cross_attention=False,  # New flag to switch between self-attention and cross-attention
        n=None,  # Specify the split point for cross-attention
        
        incremental_state=None,
        key_padding_mask=None,
        attn_mask=None,
        rel_pos=None,
        is_first_step=False,
        is_causal=False,
        coords=None,
        
    ):
        # print('dilated_attention.py 169 coords.shape',coords.shape) # torch.Size([1, 744, 2])
        # print('this_head da',this_head)

        # print('dilated_attention.py coords.shape',coords.shape)
        # print('runnning dilated attention')
        assert self.args.flash_attention
        assert rel_pos is None
        bsz, tgt_len, embed_dim = query.size()
        src_len = tgt_len
        assert embed_dim == self.embed_dim, f"query dim {embed_dim} != {self.embed_dim}"

        key_bsz, src_len, _ = key.size()
        assert key_bsz == bsz, f"{query.size(), key.size()}"
        assert value is not None
        assert bsz, src_len == value.shape[:2]

        # print('query.shape',query.shape)
        # print('key.shape',key.shape)
        # print('value.shape',value.shape)

        # print('query.shape',query.shape)
        # print('key.shape',key.shape)
        # print('value.shape',value.shape)
        # print('q in dilated_attention', query)
        # print('k in dilated_attention', key)
        # print('v in dilated_attention', value)
        if layer_n<12:
            q = self.q_proj(query)
            k = self.k_proj(key)
            v = self.v_proj(value)
            q = rearrange(q, 'b l (h d) -> (b h) l d', h=self.num_heads)
            k = rearrange(k, 'b l (h d) -> (b h) l d', h=self.num_heads)
            v = rearrange(v, 'b l (h d) -> (b h) l d', h=self.num_heads)
            
        else:
            q = self.q_proj_last_layer(query)
            k = self.k_proj_last_layer(key)
            v = self.v_proj_last_layer(value)
            q = rearrange(q, 'b l (h d) -> (b h) l d', h=self.customize_head_n)
            k = rearrange(k, 'b l (h d) -> (b h) l d', h=self.customize_head_n)
            v = rearrange(v, 'b l (h d) -> (b h) l d', h=self.customize_head_n)

        
        
        # print('0 q.shape',q.shape) # [1,xx,768]
        # print('0 k.shape',k.shape) # [1,xx,768]
        # print('0 v.shape',v.shape) # [1,xx,768]
        
        q_cls = self.q_proj_cls(query_cls)
        # print('0 q_cls.shape',q_cls.shape) # [1,1,768]
        


        import torch
        import matplotlib.pyplot as plt
        import seaborn as sns
        


        
        q_cls = rearrange(q_cls, 'b l (h d) -> (b h) l d', h=self.num_heads)

        
        # print('0 rearrange q.shape',q.shape) 
        # print('0 rearrange k.shape',k.shape)
        # print('0 rearrange v.shape',v.shape)
        # print('0 rearrange q_cls.shape',q_cls.shape)
        # 0 rearrange q.shape torch.Size([16, 82, 48])
        # 0 rearrange k.shape torch.Size([16, 82, 48])
        # 0 rearrange v.shape torch.Size([16, 82, 48])
        # 0 rearrange q_cls.shape torch.Size([16, 1, 48])
        

        if incremental_state is not None and not is_first_step:
            offset = src_len - 1
        else:
            offset = 0
            
        # print('incremental_state',incremental_state)
        # print('is_first_step',is_first_step)
        # print('offset',offset)
        # print('incremental_state',incremental_state)
        # print('self.xpos',self.xpos)
        
        if incremental_state is not None:
            # print('incremental_state is not None')# not in this if
            if "prev_key" in incremental_state:
                prev_key = incremental_state["prev_key"].view(
                    bsz * self.num_heads, -1, self.head_dim
                )
                prev_value = incremental_state["prev_value"].view(
                    bsz * self.num_heads, -1, self.head_dim
                )
                k = torch.cat([prev_key, k], dim=1)
                v = torch.cat([prev_value, v], dim=1)
            incremental_state["prev_key"] = k.view(
                bsz, self.num_heads, -1, self.head_dim
            )
            incremental_state["prev_value"] = v.view(
                bsz, self.num_heads, -1, self.head_dim
            )
            src_len = k.size(1)

        if self.xpos is not None: # it is none
            if incremental_state is not None and not is_first_step:
                offset = src_len - 1
            else:
                offset = 0
            k = self.xpos(k, offset=0, downscale=True)
            q = self.xpos(q, offset=offset, downscale=False)

        
        if layer_n<12:
            q = rearrange(q, '(b h) l d -> b l h d', h=self.num_heads)
            k = rearrange(k, '(b h) l d -> b l h d', h=self.num_heads)
            v = rearrange(v, '(b h) l d -> b l h d', h=self.num_heads)
        else:
            q = rearrange(q, '(b h) l d -> b l h d', h=self.customize_head_n)
            k = rearrange(k, '(b h) l d -> b l h d', h=self.customize_head_n)
            v = rearrange(v, '(b h) l d -> b l h d', h=self.customize_head_n)
            
        q_cls = rearrange(q_cls, '(b h) l d -> b l h d', h=self.num_heads)


        # print('1 rearrange q.shape',q.shape) 
        # print('1 rearrange k.shape',k.shape)
        # print('1 rearrange v.shape',v.shape)
        # print('1 rearrange q_cls.shape',q_cls.shape)
        
        # 1 rearrange q.shape torch.Size([1, 68, 16, 48])
        # 1 rearrange k.shape torch.Size([1, 68, 16, 48])
        # 1 rearrange v.shape torch.Size([1, 68, 16, 48])
        # 1 rearrange q_cls.shape torch.Size([1, 1, 16, 48])

        
        def plot_tensor(tensor_gpu,title=''):
            import matplotlib.pyplot as plt
            # Move the tensor to CPU and convert to NumPy
            tensor_cpu = tensor_gpu.detach().to('cpu').numpy()
            
            # Plot the distribution
            plt.hist(tensor_cpu, bins=30, edgecolor='black')
            plt.title(title)
            plt.xlabel("Value")
            plt.ylabel("Frequency")
            # Save plot with datetime
            save_dir = 'results/attention/plot_results'
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
            os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
            save_path = os.path.join(save_dir, f'DilatedAttention_tensor_{timestamp}.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved plot to {save_path}")
            plt.close() 

        import matplotlib.pyplot as plt 


        activate_dilate=False
        
        if activate_dilate:
            outs, lses = [], []
            
            # print('self.args.segment_length',self.args.segment_length)
            # print('self.args.dilated_ratio',self.args.dilated_ratio)
            
            for sl, dr in zip(self.args.segment_length, self.args.dilated_ratio):
                # print('sl: ',sl,'dr: ',dr)
                ki = self.gathering(k, dr, sl, is_causal=is_causal, offset=0, is_kv=True, seq_parall=self.args.seq_parallel)
                vi = self.gathering(v, dr, sl, is_causal=is_causal, offset=0, is_kv=True, seq_parall=self.args.seq_parallel)
                qi = self.gathering(q, dr, sl, is_causal=is_causal, offset=offset, is_kv=False, seq_parall=self.args.seq_parallel)
                # print('qi.shape',qi.shape)
                # print('qi',qi)
                # print('ki.shape',ki.shape)
                # print('ki',ki)
                # print('vi.shape',vi.shape)
                # print('vi',vi)
                out, lse = self.attention_ops(qi, ki, vi, 
                                              key_padding_mask=key_padding_mask, 
                                              attn_mask=attn_mask, 
                                              rel_pos=rel_pos, 
                                              is_causal=is_causal)
    
    
                outs.append(out)
                # print('out.shape',out.shape)
                lses.append(lse)
            # self.attn0=outs
            # print('outs.shape',outs.shape)
            # print('tgt_len',tgt_len)
            # print('bsz',bsz)
            # print('offset',offset)
            
            attn = self.scattering(outs, lses, tgt_len, bsz, offset=offset)
            # self.attn1=attn
            # print('attn1.shape',attn.shape)
    
    
            if self.inner_attn_ln is not None:
                
                attn = self.inner_attn_ln(attn)
                # self.attn2=attn
                # print('attn2.shape',attn.shape)
            
            attn = self.out_proj(attn)
            # print('attn3.shape',attn.shape)
            # self.attn3=attn

    
        def save_attn_by_time(tensor):
            from datetime import datetime

            print('saving tensor')
            # Get current timestamp with milliseconds
            current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")[:-3]  # Truncate microseconds to milliseconds
            
            # Define the filename with timestamp
            filename = f"tensor_{current_time}.pt"
            
            # Save the tensor
            torch.save(tensor, filename)


            
        ################################################
        ####### smaller max_tile_amt will cause error
        ################################################

        max_tile_amt=[50000]
        ratio_1=[1]
        
        standard_outs, standard_lses, standard_attn_prob = [], [], []######
        standard_outs_cls, standard_lses_cls, standard_attn_prob_cls = [], [], []######

        
        for sl, dr in zip(max_tile_amt, ratio_1): # only one epoch
            # print('sl: ',sl,'dr: ',dr)
            ki = self.gathering(k, dr, sl, is_causal=is_causal, offset=0, is_kv=True, seq_parall=self.args.seq_parallel)
            vi = self.gathering(v, dr, sl, is_causal=is_causal, offset=0, is_kv=True, seq_parall=self.args.seq_parallel)
            qi = self.gathering(q, dr, sl, is_causal=is_causal, offset=offset, is_kv=False, seq_parall=self.args.seq_parallel)

            attn_mode='multi_head' 
            
            if layer_n<12:
                attn_mode='flash_attn'
            else:
                attn_mode='standard_with_score'
            standard_out, standard_lse, attn_prob_mtx= self.attention_ops(qi, ki, vi, 
                                                    attn_mode_end=attn_mode,
                                                    layer_n=layer_n,
                                                    key_padding_mask=key_padding_mask, 
                                                    attn_mask=attn_mask, 
                                                    rel_pos=rel_pos, 
                                                    is_causal=is_causal,attn_mode=attn_mode)
            
            qi_cls=qi[:, 0:1, :]
            ki_cls=qi[:, 1:, :]
            vi_cls=qi[:, 1:, :]
            
            standard_out_cls, standard_lse_cls, attn_prob_mtx_cls = self.attention_ops(qi_cls, ki_cls, vi_cls, 
                                                    key_padding_mask=key_padding_mask, 
                                                    layer_n=layer_n,
                                                    attn_mask=attn_mask, 
                                                    rel_pos=rel_pos, 
                                                    is_causal=is_causal,attn_mode=attn_mode)
            # print('standard_out_cls.shape', standard_out_cls.shape) # [1,1,768]
            # print('standard_lse_cls.shape', standard_lse_cls.shape) # [1,16,70]
            # print('attn_prob_mtx_cls.shape', attn_prob_mtx_cls.shape) # [1,16,1,69]

            standard_outs.append(standard_out)
            standard_lses.append(standard_lse)
            standard_attn_prob.append(attn_prob_mtx)

            standard_outs_cls.append(standard_out_cls)
            standard_lses_cls.append(standard_lse_cls)
            standard_attn_prob_cls.append(attn_prob_mtx_cls)
            
            # print('attn_prob_mtx.shape',attn_prob_mtx.shape)

        if (layer_n==12) and (attn_mode=='multi_head'):
            # print('multi head for layer', layer_n)
            attn_standard=standard_out[:,:,this_head,:] # torch.Size([1, 5456, 16, 48])
            attn_standard_cls = self.out_proj_cls_customize_head(standard_out_cls)
        else:
            # print('normal for layer', layer_n)
            # print('standard_out.shape',standard_out.shape)
            attn_standard = self.scattering(standard_outs, 
                                standard_lses, 
                                tgt_len, 
                                bsz, 
                                offset=offset,type_label='standard') # always standard after changing sl

            if self.inner_attn_ln is not None:
                # attn_standard_before=attn_standard
                attn_standard = self.inner_attn_ln(attn_standard)
            
            attn_standard = self.out_proj(attn_standard) # note: this code was attn = self.out_proj(attn_standard) which meant not output before (now20250213,developing multi-head, then found this point --sl)
            attn_standard_cls = self.out_proj_cls(standard_out_cls)
    
        return attn_standard, None, attn_prob_mtx, standard_out_cls, None, attn_prob_mtx_cls  # results go to encoder.py

