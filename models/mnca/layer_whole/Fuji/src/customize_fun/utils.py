
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime

def visualize_heatmap(tensor: torch.Tensor, cmap: str = "viridis", title: str = "Heatmap"):
    """
    Visualize a 2D torch.Tensor as a heatmap using matplotlib + seaborn.
    
    Args:
        tensor (torch.Tensor): A 2D tensor of shape (rows, cols).
        cmap (str): The colormap to use (e.g. 'viridis', 'hot', 'coolwarm').
        title (str): Title for the heatmap figure.


    # --------------------------
    # Example usage:
    # --------------------------
    if __name__ == "__main__":
        # Create a random 2D tensor
        example_tensor = torch.randn(10, 15)  # shape (10 rows, 15 cols)
    
        # Visualize the tensor as a heatmap
        visualize_heatmap(example_tensor, cmap="coolwarm", title="Random Tensor Heatmap")

    """
    if tensor.dim() != 2:
        raise ValueError("The input 'tensor' must be 2D.")
    
    # Convert tensor to NumPy
    data = tensor.detach().cpu().numpy()
    
    # Create the plot
    plt.figure(figsize=(20, 20))
    sns.heatmap(data, cmap=cmap, annot=False)  # annot=True to show numeric values
    plt.title(title)
    plt.xlabel("Columns")
    plt.ylabel("Rows")
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'visualize_heatmap_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 

def print_overlap_amount(list1, list2, list3):
    """
    This function takes three lists as input and prints the number of overlapping elements
    between each pair of the lists.


# Example usage
list1 = [1, 2, 3, 4, 5]
list2 = [4, 5, 6, 7]
list3 = [5, 6, 7, 8, 9]

print_overlap_amount(list1, list2, list3)

    """
    # Calculate overlap
    overlap_1_2 = len(set(list1) & set(list2))
    overlap_1_3 = len(set(list1) & set(list3))
    overlap_2_3 = len(set(list2) & set(list3))

    # Print results
    print(f"Overlap between list1 and list2: {overlap_1_2} elements")
    print(f"Overlap between list1 and list3: {overlap_1_3} elements")
    print(f"Overlap between list2 and list3: {overlap_2_3} elements")
