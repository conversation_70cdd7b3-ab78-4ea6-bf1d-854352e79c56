import torch
import torch.nn as nn
from copy import deepcopy
import traceback  
import os, sys
import time
import wandb
import torch
import numpy as np
import csv
import torch.utils.tensorboard as tensorboard
import gc

from src.gigapath_0.classification_head import get_model
from src.metrics import calculate_metrics_with_task_cfg
from src.utils import get_optimizer, get_loss_function, Monitor_Score, get_records_array, log_writer, adjust_learning_rate

from distill_giga import GigaOneLayer


def check_model_grad(md):
    for name, param in md.named_parameters():
        if param.grad is None:
            print(f"No gradient for {name}")
        else:
            print(f"{name}: grad mean = {param.grad.mean().item()}")


def train(dataloader, fold, args, model_dir='', opt_model_dir=''): 
    
    # set up the writer
    writer_dir = os.path.join(args.save_dir, f'fold_{fold}', 'tensorboard')
    if not os.path.isdir(writer_dir):
        os.makedirs(writer_dir, exist_ok=True)
    writer = tensorboard.SummaryWriter(writer_dir, flush_secs=15)
    if "wandb" in args.report_to:
        wandb.init(
            project=args.exp_code,
            name=args.exp_code + '_fold_' + str(fold),
            id='fold_' + str(fold),
            tags=[],
            config=vars(args),
        )
        writer = wandb
    elif "tensorboard" in args.report_to:
        writer = tensorboard.SummaryWriter(writer_dir, flush_secs=15)

    # set up the dataloader
    train_loader_lst, val_loader_lst, test_loader_lst = dataloader

    # set up the model
    model = get_model(**vars(args)) # gigapath model

    # update the cls token
    initial_cls_token = model.slide_encoder.cls_token.detach().clone()
    model.slide_encoder.cls_tokens[0]=nn.Parameter(initial_cls_token.clone(), requires_grad=True) ##### saved model have cls_tokens[0], need prepare even if not used

    # append the new layer to the encoder layers
    new_layer = deepcopy(model.slide_encoder.encoder.layers[-1])  # Copy the structure of the last existing layer 13th
    new_layer.apply(lambda m: m.reset_parameters() if hasattr(m, 'reset_parameters') else None)  # Reset weights of layer 13th
    model.slide_encoder.encoder.layers.append(new_layer) # Add the new layer as layer 13th
    
    # Verify the structure update
    print('model after add layer', model)
    print('model.slide_encoder.global_pool', model.slide_encoder.global_pool)
    
    # load model parameters from step 1
    if args.train_from_checkpoint_giga_and_customize12Layer: # False, 0 epoch have no checkpoint, others will use
        print('training from checkpoint')
        print('loading', model_dir,'for gigapath and layer 12')
        model.load_state_dict(torch.load(model_dir), strict=False)
    else:
        if model_dir=="":
            print('training from scratch')
        # contrastive learning layer define and load weights
        print('loading 12th layer')
        print('replacing distill giga layer')
        one_layer_verify = GigaOneLayer('pretrained/20250703_model_full.pth').to(args.device) # load gigapath 12th layer
        one_layer_verify = nn.DataParallel(one_layer_verify)
        checkpoint = torch.load('pretrained/epoch_12_batch_75.pt', map_location=args.device)
        one_layer_verify.load_state_dict(checkpoint['model_one_layer_state_dict'])
        
        model.slide_encoder.encoder.layers[11] = deepcopy(one_layer_verify.module.layer) # replace 12th layer
    model = model.to(args.device)


    # set up the optimizer
    optimizer = get_optimizer(args, model)
    if opt_model_dir=='':
        print('initing optimizer')
    else:
        print('loading optimizer from', opt_model_dir)
        
        optimizer.load_state_dict(torch.load(opt_model_dir))
    
    # set up the loss
    loss_fn = get_loss_function(args.task_config) # gigapath loss, classification loss 2
    
    # set up the monitor
    monitor = Monitor_Score()
    
    # set up the fp16 scaler
    fp16_scaler = None
    if args.fp16:
        fp16_scaler = torch.cuda.amp.GradScaler()  #### gigapath sclar use later
        print('Using fp16 training')
    for enu_loader, (train_loader, val_loader, test_loader) in enumerate(zip(train_loader_lst,val_loader_lst, test_loader_lst)):
        print('enu_loader',enu_loader)
        print('Training on {} samples'.format(len(train_loader.dataset)))
        print('Validating on {} samples'.format(len(val_loader.dataset))) if val_loader is not None else None
        print('Testing on {} samples'.format(len(test_loader.dataset))) if test_loader is not None else None
        print('Training starts!')

    val_records, test_records = None, None
    start_seconds = time.time()

    if args.train_mode=='train':
        for i in range(args.epochs):
            args.current_epoch=i
            for enu_loader, (train_loader, val_loader, test_loader) in enumerate(zip(train_loader_lst,val_loader_lst,test_loader_lst)):

                print('Epoch: {} No. of enu_loader {}'.format(i, enu_loader))
                print('==============running!!!')
                train_records, train_attn_prob_mtx = train_one_epoch(train_loader, model, fp16_scaler, optimizer, loss_fn, i, args)

                save_path=os.path.join(args.save_dir, 'epoch_'+str(i)+'fold_' + str(fold)+'_enuLoader_'+str(enu_loader)+"_checkpoint.pt")
                save_path_opt=os.path.join(args.save_dir, 'epoch_'+str(i)+'fold_' + str(fold)+'_enuLoader_'+str(enu_loader)+"_opt_checkpoint.pth")
                print('saving model to ',save_path)
                torch.save(model.state_dict(), save_path)
                # print('args.model_select',args.model_select)
                print('saving optimizer to ',save_path_opt)
                torch.save(optimizer.state_dict(), save_path_opt)

        end_seconds = time.time()
        elapsed_seconds = end_seconds - start_seconds
        print("Seconds passed:", elapsed_seconds)
    else:
        print('=======Evaluating mode we are not training')

    
    model.eval()
    
    train_records = dict()
    train_attn_prob_mtx=dict()
    
    val_records = dict()
    val_attn_prob_mtx=dict()
    
    test_records =dict()
    test_attn_prob_mtx=dict()
    

    for enu_loader, (train_loader, val_loader, test_loader) in enumerate(zip(train_loader_lst, val_loader_lst, test_loader_lst)):
        print('Evaluating enu_loader', enu_loader)

        if args.eval_dict['train']==1:
            print('===Evaluating training dataset. Note that if training data is very large, we recommend you to load the saved model and adjust  args.eval_dict')
            train_records_one, train_attn_prob_mtx_one = evaluate(train_loader, model, fp16_scaler, loss_fn, i, args)
            train_records[enu_loader]=train_records_one
            train_attn_prob_mtx[enu_loader]=train_attn_prob_mtx_one
        else:
            print('===Not Evaluating training dataset')
            train_records=None
            train_attn_prob_mtx=None
        
        if args.eval_dict['val']==1:
            print('===Evaluating val dataset')
            val_records_one, val_attn_prob_mtx_one = evaluate(val_loader, model, fp16_scaler, loss_fn, i, args)
            val_records[enu_loader]=val_records_one
            val_attn_prob_mtx[enu_loader]=val_attn_prob_mtx_one
        else:
            print('===Not Evaluating val dataset')
            val_records=None
            val_attn_prob_mtx=None
            
        if args.eval_dict['test']==1:
            print('===Evaluating test dataset')
            test_records_one, test_attn_prob_mtx_one = evaluate(test_loader, model, fp16_scaler, loss_fn, i, args)
            test_records[enu_loader]=test_records_one
            test_attn_prob_mtx[enu_loader]=test_attn_prob_mtx_one
        else:
            print('===Not Evaluating test dataset')
            test_records=None
            test_attn_prob_mtx=None

    return train_records, train_attn_prob_mtx, val_records, val_attn_prob_mtx, test_records, test_attn_prob_mtx



def train_one_epoch(train_loader, model, fp16_scaler, optimizer, loss_fn, epoch, args):
    if args.train_mode == 'train':
        model.train()
    else:
        model.eval()

    start_time = time.time()
    seq_len = 0
    records = get_records_array(len(train_loader), args.n_classes)
    dict_record = dict()
    bad_sample_log = []
    
    for batch_idx, batch in enumerate(train_loader):
        try:
            args.slide_id = batch['slide_id']
            dict_record[batch_idx] = args.slide_id
            args.current_batch_idx = batch_idx
            args.epoch = epoch

            if batch_idx % args.gc == 0 and args.lr_scheduler == 'cosine':
                adjust_learning_rate(optimizer, batch_idx / len(train_loader) + epoch, args)

            # warmup learning rate, force learning rate after force_lr_after_e0 is not None
            if epoch > 0 and args.force_lr_after_e0 is not None:
                for enu_para, param_group in enumerate(optimizer.param_groups):
                    optimizer.param_groups[enu_para]['lr'] = args.force_lr_after_e0

            images, img_coords, label, sig_type, attn, images_full, img_coords_full, loss_classify_ratio, loss_attn_ratio = (
                batch['imgs'], batch['coords'], batch['labels'], batch['type'][0], batch['attn'],
                batch['imgs_full'], batch['coords_full'], batch['loss_classify_ratio'], batch['loss_attn_ratio']
            )
            args.label=label

            images = images.to(args.device, non_blocking=True)
            img_coords = img_coords.to(args.device, non_blocking=True)
            label = label.to(args.device, non_blocking=True).long()
            attn = attn.to(args.device, non_blocking=True)
            loss_classify_ratio = loss_classify_ratio.to(args.device, non_blocking=True)
            loss_attn_ratio = loss_attn_ratio.to(args.device, non_blocking=True)
            
            # full image for reconstruction
            images_full = images_full.to(args.device, non_blocking=True)
            img_coords_full = img_coords_full.to(args.device, non_blocking=True)

            seq_len += images.shape[1]

            # loss prediction
            with torch.cuda.amp.autocast(dtype=torch.float16 if args.fp16 else torch.float32):
                clasification_loss, ms_loss, attn_prob_loss, st_loss, attn_loss, reconstruct_loss, st_loss, _ = model( 
                    images, img_coords, args, attn, images_full, img_coords_full,
                    this_head=args.sig_type_head_dict[sig_type]
                )

                label = label.squeeze(-1).float() if isinstance(loss_fn, torch.nn.BCEWithLogitsLoss) else label.squeeze(-1).long()

                if args.w2 == 1 or args.w3 == 1:
                    loss1 = loss_fn(clasification_loss, label) * args.w1 # gigapath loss, no use set w1=0 in args
                    loss2 = loss_fn(ms_loss, label) * args.w2 * loss_classify_ratio
                    loss3 = attn_loss * args.w3 * loss_attn_ratio if loss_attn_ratio != 0 else torch.tensor(0, device=args.device)
                else:
                    loss1 = torch.tensor(0, device=args.device)
                    loss2 = torch.tensor(0, device=args.device)
                    loss3 = torch.tensor(0, device=args.device)

                loss4 = reconstruct_loss * args.w4
                loss5 = args.w5 * st_loss
                loss = loss1 + loss2 + loss3 + loss4 + loss5

            if fp16_scaler is None:
                loss.backward()
                if (batch_idx + 1) % args.gc == 0:
                    optimizer.step()
                    optimizer.zero_grad()
            else:
                fp16_scaler.scale(loss).backward()
                if (batch_idx + 1) % args.gc == 0:
                    fp16_scaler.step(optimizer)
                    fp16_scaler.update()
                    optimizer.zero_grad()

            if args.train_mode == 'train' and ((batch_idx + 1) == 1 or (batch_idx + 1) % args.batch_save_node == 0): #### for debugging embedding performance
                save_path = os.path.join(args.save_dir, f'epoch_{epoch}batch_{batch_idx}_checkpoint.pt')
                save_path_opt = os.path.join(args.save_dir, f'epoch_{epoch}batch_{batch_idx}_opt_checkpoint.pth')
                print('saving model to', save_path)
                torch.save(model.state_dict(), save_path)
                torch.save(optimizer.state_dict(), save_path_opt)
                torch.cuda.empty_cache()
                gc.collect()

            records['loss'] += loss.cpu().item() * args.gc
            records['loss1'] += loss1.cpu().item() * args.gc
            records['loss2'] += loss2.cpu().item() * args.gc
            records['loss3'] += loss3.cpu().item() * args.gc
            records['loss4'] += loss4.cpu().item() * args.gc
            records['loss5'] += loss5.cpu().item() * args.gc
                            
            # print(f'all: {records['loss']} gita default: {records['loss1']} ms classification: {records['loss2']} attention distribution: {records['loss3']} reconstruction: {records['loss4']} st: {records['loss5']}')
            writer.add_scalar('Test all', records['loss'], epoch)
            writer.add_scalar('Test giga default', records['loss1'], epoch)
            writer.add_scalar('Test ms classification', records['loss12'], epoch)
            writer.add_scalar('Test attention distribution', records['loss3'], epoch)
            writer.add_scalar('Test reconstruction', records['loss4'], epoch)
            writer.add_scalar('Test st', records['loss5'], epoch)
            # print('batch_idx',batch_idx,'args.batch_show_node',args.batch_show_node)
            if (batch_idx + 1) % args.batch_show_node == 0:
                log_str = 'sig_type: {};Epoch: {}; Batch: {}; loss {:.6f}; loss1 {:.6f}; loss2 {:.6f}; loss3 {:.6f}; loss4 {:.6f}; lr {:.8f}; id {}; label {}; loss_classify_ratio {}; ms_loss {}; st_loss {};'.format(
                    sig_type, epoch, batch_idx, loss.item(), loss1.item(), loss2.item(), loss3.item(), loss4.item(), loss5.item(),
                    optimizer.param_groups[0]['lr'], args.slide_id, label, loss_classify_ratio, ms_loss)

                # log to file
                log_path = os.path.join(args.save_dir, "training_log.txt")
                with open(log_path, "a") as f:
                    f.write(log_str + "\n")

            if (batch_idx + 1) % 5001 == 0:
                print(log_str)

        except Exception as e:
            slide_id = batch.get('slide_id', 'Unknown')
            print(f"\n❌ Skipping batch {batch_idx} (slide_id: {slide_id}, sig_type: {sig_type} ) due to error:\n    {str(e)}\n")
            bad_sample_log.append([epoch, batch_idx, str(slide_id), str(e)])
            traceback.print_exc()  # 打印完整的堆栈追踪
            continue

    records['loss'] = records['loss']
    print('Epoch: {}, Loss: {:.6f}, Loss1: {:.6f}, Loss2: {:.6f}, Loss3: {:.6f}, Loss4: {:.6f}, Loss5: {:.6f}'.format(
        epoch, records['loss'], records['loss1'], records['loss2'], records['loss3'], records['loss4'], records['loss5']))

    # saving bad samples
    if bad_sample_log:
        tsv_path = os.path.join(args.save_dir, f"bad_samples_epoch{epoch}.tsv")
        with open(tsv_path, "w", newline="") as f:
            writer = csv.writer(f, delimiter='\t')
            writer.writerow(["epoch", "batch_idx", "slide_id", "error"])
            writer.writerows(bad_sample_log)
        print(f"⚠️ Bad samples in epoch {epoch} saved to: {tsv_path}")

    return records, attn_prob_loss




def evaluate(loader, model, fp16_scaler, loss_fn, epoch, args, show_log=True):
    if not hasattr(args, 'current_batch_idx'):
        args.current_batch_idx = -1

    model.eval()

    records = get_records_array(len(loader), args.n_classes)
    task_setting = args.task_config.get('setting', 'multi_class')
    output_file = args.eval_output_Path

    with open(output_file, "w") as f_out:
        f_out.write("slide_id\tsig_type\tlabel\tpredicted\tprob\tloss1\tloss2\tloss3\tloss4\n")

    bad_sample_log = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(loader):
            try:
                args.slide_id = batch['slide_id']
                images, img_coords, label, sig_type, attn, images_full, img_coords_full, loss_classify_ratio, loss_attn_ratio = (
                    batch['imgs'], batch['coords'], batch['labels'], batch['type'][0], batch['attn'],
                    batch['imgs_full'], batch['coords_full'], batch['loss_classify_ratio'], batch['loss_attn_ratio']
                )
                args.label=label
                images = images.to(args.device, non_blocking=True)
                img_coords = img_coords.to(args.device, non_blocking=True)
                label = label.to(args.device, non_blocking=True).long()
                attn = attn.to(args.device, non_blocking=True)
                loss_classify_ratio = loss_classify_ratio.to(args.device, non_blocking=True)
                loss_attn_ratio = loss_attn_ratio.to(args.device, non_blocking=True)
                images_full = images_full.to(args.device, non_blocking=True)

                with torch.cuda.amp.autocast(fp16_scaler is not None, dtype=torch.float16):
                    clasification_loss, ms_loss, attn_prob_loss, st_loss, attn_loss, reconstruct_loss, _ = model(
                        images, img_coords, args, attn, images_full, img_coords_full,
                        this_head=args.sig_type_head_dict[sig_type]
                    )

                    if isinstance(loss_fn, torch.nn.BCEWithLogitsLoss):
                        label = label.squeeze(-1).float()
                    else:
                        label = label.squeeze(-1).long()

                    if args.w2 == 1 or args.w3 == 1:
                        loss1 = loss_fn(clasification_loss, label) * args.w1
                        loss2 = loss_fn(ms_loss, label) * args.w2 * loss_classify_ratio
                        loss3 = attn_loss * args.w3 * loss_attn_ratio if loss_attn_ratio != 0 else torch.tensor(0, device=args.device)
                    else:
                        loss1 = torch.tensor(0, device=args.device)
                        loss2 = torch.tensor(0, device=args.device)
                        loss3 = torch.tensor(0, device=args.device)

                    loss4 = reconstruct_loss * args.w4
                    loss = loss1 + loss2 + loss3 + loss4

                records['loss'] += loss.cpu().item()
                records['loss1'] += loss1.cpu().item()
                records['loss2'] += loss2.cpu().item()
                records['loss3'] += loss3.cpu().item()
                records['loss4'] += loss4.cpu().item()

                slide_ids = batch.get('slide_id', ['N/A'] * label.size(0))
                softmax_probs = torch.softmax(ms_loss, dim=1).cpu()
                predicted_labels = torch.argmax(softmax_probs, dim=1)

                if (batch_idx + 1) % 500 == 0:
                    print(f"\n[Evaluation Progress] n={batch_idx + 1}")
                    print(
                        f"slide_id: {slide_ids[0]}\t"
                        f"sig_type: {sig_type}\t"
                        f"label: {label[0].item()}\t"
                        f"predicted: {predicted_labels[0].item()}\t"
                        f"prob: {softmax_probs[0].tolist()}\t"
                        f"loss1: {loss1.item():.4f}\t"
                        f"loss2: {loss2.item():.4f}\t"
                        f"loss3: {loss3.item():.4f}\t"
                        f"loss4: {loss4.item():.4f}"
                    )

                with open(output_file, "a") as f_out:
                    for i in range(label.size(0)):
                        f_out.write(
                            f"{slide_ids[i]}\t"
                            f"{sig_type}\t"
                            f"{label[i].item()}\t"
                            f"{predicted_labels[i].item()}\t"
                            f"{','.join(map(str, softmax_probs[i].tolist()))}\t"
                            f"{loss1.item():.4f}\t"
                            f"{loss2.item():.4f}\t"
                            f"{loss3.item():.4f}\t"
                            f"{loss4.item():.4f}\n"
                        )

                if task_setting == 'multi_label':
                    Y_prob = torch.sigmoid(clasification_loss)
                    records['prob'][batch_idx] = Y_prob.cpu().numpy()
                    records['label'][batch_idx] = label.cpu().numpy()
                elif task_setting in ['multi_class', 'binary']:
                    Y_prob1 = torch.softmax(clasification_loss, dim=1).cpu()
                    Y_prob2 = softmax_probs
                    records['prob1'][batch_idx] = Y_prob1.numpy()
                    records['prob2'][batch_idx] = Y_prob2.numpy()
                    label_1 = torch.zeros_like(Y_prob1).scatter_(1, label.cpu().unsqueeze(1), 1)
                    label_2 = torch.zeros_like(Y_prob2).scatter_(1, label.cpu().unsqueeze(1), 1)
                    records['label1'][batch_idx] = label_1.numpy()
                    records['label2'][batch_idx] = label_2.numpy()

            except Exception as e:
                slide_id = batch.get('slide_id', 'Unknown')
                print(f"\n❌ Skipping batch {batch_idx} (slide_id: {slide_id}) due to error:\n    {str(e)}\n")
                bad_sample_log.append([epoch, batch_idx, str(slide_id), str(e)])
                traceback.print_exc()  # print the full stack trace
                continue

    # save bad sample log
    if bad_sample_log:
        tsv_path = os.path.join(args.save_dir, f"bad_samples_eval_epoch{epoch}.tsv")
        with open(tsv_path, "w", newline="") as f:
            writer = csv.writer(f, delimiter='\t')
            writer.writerow(["epoch", "batch_idx", "slide_id", "error"])
            writer.writerows(bad_sample_log)
        print(f"⚠️ Evaluation bad samples saved to: {tsv_path}")

    if show_log:
        records.update(calculate_metrics_with_task_cfg(records['prob1'], records['label1'], args.task_config))
        records.update(calculate_metrics_with_task_cfg(records['prob2'], records['label2'], args.task_config))
        info = 'Epoch: {}, Loss: {:.4f},Loss1: {:.4f},Loss2: {:.4f},Loss3: {:.4f},Loss4: {:.4f}, AUROC: {:.4f}, ACC: {:.4f}, BACC: {:.4f}'.format(
            epoch, records['loss'], records['loss1'], records['loss2'], records['loss3'], records['loss4'],
            records['macro_auroc'], records['acc'], records['bacc']
        )
        for metric in args.task_config.get('add_metrics', []):
            info += ', {}: {:.4f}'.format(metric, records[metric])
        print(info)

    return records, attn_prob_loss



def setup_model_optimizer_loss(args):
    """
    Set up the model, optimizer, and loss function.

    Args:
        args: An object or dictionary containing model, optimizer, and task configuration parameters.

    Returns:
        model: The initialized model moved to the specified device.
        optimizer: The optimizer for the model.
        loss_fn: The loss function for the task.
    """
    # Set up the model
    model = get_model(**vars(args))
    model = model.to(args.device)

    # Set up the optimizer
    optimizer = get_optimizer(args, model)

    # Set up the loss function
    loss_fn = get_loss_function(args.task_config)

    return model, optimizer, loss_fn
