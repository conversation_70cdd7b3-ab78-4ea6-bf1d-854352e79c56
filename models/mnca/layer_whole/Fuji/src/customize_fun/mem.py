import os
import torch
import pandas as pd
import numpy as np
import torch
import torch.nn as nn


def check_cuda_memory():
    """
    Prints the current, allocated, and cached CUDA memory usage.
    """
    if not torch.cuda.is_available():
        print("CUDA is not available on this system.")
        return

    device = torch.device("cuda:0")
    print(f"CUDA Device: {torch.cuda.get_device_name(device)}")

    # Memory statistics
    memory_allocated = torch.cuda.memory_allocated(device) / (1024 ** 2)  # In MB
    max_memory_allocated = torch.cuda.max_memory_allocated(device) / (1024 ** 2)  # In MB
    memory_reserved = torch.cuda.memory_reserved(device) / (1024 ** 2)  # In MB
    max_memory_reserved = torch.cuda.max_memory_reserved(device) / (1024 ** 2)  # In MB
    memory_free = torch.cuda.get_device_properties(device).total_memory / (1024 ** 2) - memory_reserved

    print(f"Allocated Memory: {memory_allocated:.2f} MB")
    print(f"Max Allocated Memory: {max_memory_allocated:.2f} MB")
    print(f"Reserved Memory: {memory_reserved:.2f} MB")
    print(f"Max Reserved Memory: {max_memory_reserved:.2f} MB")
    print(f"Free Memory: {memory_free:.2f} MB")
