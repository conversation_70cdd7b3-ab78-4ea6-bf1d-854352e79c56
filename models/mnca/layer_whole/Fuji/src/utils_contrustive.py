import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import numpy as np
import plotly.express as px
import plotly.subplots as sp
import plotly.graph_objects as go
import umap
import pickle  # to save cancer_labels
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from tqdm import tqdm
import copy

import re
import gc
import traceback
import random
import sys
import random
from torch.cuda.amp import GradScaler, autocast

from src.models import *
from src.dataset_ffpe_ff import *



# Plot
def plot_embeddings(proj_a, proj_b, epoch, save_dir=None):
    proj_a_2d = PCA(n_components=2).fit_transform(proj_a.cpu().detach().numpy())
    proj_b_2d = PCA(n_components=2).fit_transform(proj_b.cpu().detach().numpy())
    
    plt.figure(figsize=(8,6))
    plt.scatter(proj_a_2d[:,0], proj_a_2d[:,1], label='Gigapath', alpha=0.6)
    plt.scatter(proj_b_2d[:,0], proj_b_2d[:,1], label='Chief', alpha=0.6)
    plt.legend()
    plt.title(f'Projected Embeddings at Epoch {epoch}')
    plt.xlabel('PC1')
    plt.ylabel('PC2')
    plt.grid(True)
    # Save plot if save_dir is provided
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'embeddings_epoch_{epoch}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 


def plot_embeddings_all(
    proj_a, proj_b, epoch, labels_gigapath, labels_chief,
    color_dict_cancer=None, color_dict_source=None,
    all_modes=None,
    all_source_labels=None,
    save_dir="./embedding_saves",  # optional argument to control save path,
    dict_gigapath_or_chief=None,
    dict_ffpe_or_fresh=None
):

    try:
        os.makedirs(save_dir, exist_ok=True)
    except Exception as e:
        print(f"Error creating directory: {e}")

    proj_a_np = proj_a.cpu().detach().numpy()
    proj_b_np = proj_b.cpu().detach().numpy()

    embeddings = np.vstack([proj_a_np, proj_b_np])

    # Labels
    print('len(labels_gigapath)',len(labels_gigapath), 'len(labels_chief)',len(labels_chief))
    cancer_labels = np.concatenate([labels_gigapath, labels_chief])

    source_labels = np.array(['Gigapath'] * len(proj_a_np) + all_source_labels)
    FFPE_or_Fresh_labels = np.array(['ffpe'] * len(proj_a_np) + all_modes)

    print('len(cancer_labels)',len(cancer_labels))
    print('len(source_labels)',len(source_labels))
    print('len(all_source_labels)',len(all_source_labels))
    
    print('1 len(FFPE_or_Fresh_labels)',len(['ffpe'] * len(proj_a_np)))
    print('2 len(FFPE_or_Fresh_labels)',len(all_modes))
    print('len(FFPE_or_Fresh_labels)',len(FFPE_or_Fresh_labels))
    
    if save_dir != '':
        # Save cancer_labels
        cancer_labels_save_path = os.path.join(save_dir, f'cancer_labels_epoch_{epoch}.pkl')
        with open(cancer_labels_save_path, 'wb') as f:
            pickle.dump(cancer_labels, f)
        print(f"Saved cancer_labels to {cancer_labels_save_path}")

        # Save original embeddings
        embeddings_save_path = os.path.join(save_dir, f'embeddings_epoch_{epoch}.npy')
        np.save(embeddings_save_path, embeddings)
        print(f"Saved original embeddings to {embeddings_save_path}")

    reducers = {
        'PCA': PCA(n_components=2),
        't-SNE': TSNE(n_components=2, random_state=42),
        'UMAP': umap.UMAP(n_components=2, random_state=42)
    
    }

    fig = sp.make_subplots(
        rows=3, cols=3,
        subplot_titles=[
            'PCA - Cancer_Type', 't-SNE - Cancer_Type', 'UMAP - Cancer_Type',
            'PCA - Source', 't-SNE - Source', 'UMAP - Source',
            'PCA - Mode', 't-SNE - Mode', 'UMAP - Mode',
        ],
        horizontal_spacing=0.05, vertical_spacing=0.1
    )

    for col, (name, reducer) in enumerate(reducers.items(), start=1):
        reduced = reducer.fit_transform(embeddings)

        # Save the reduced embeddings
        if save_dir != '':
            reduced_save_path = os.path.join(save_dir, f'{name.lower()}_epoch_{epoch}.npy')
            np.save(reduced_save_path, reduced)
            print(f"Saved {name} embeddings to {reduced_save_path}")

        # Row 1: Cancer Type Coloring
        for label in np.unique(cancer_labels):
            idx = cancer_labels == label
            color = color_dict_cancer[label] if (color_dict_cancer and label in color_dict_cancer) else None
            fig.add_trace(
                go.Scattergl(
                    x=reduced[idx, 0],
                    y=reduced[idx, 1],
                    mode='markers',
                    marker=dict(size=5, color=color),
                    name=f'Cancer: {label}',
                    legendgroup=f'Cancer_{label}',
                    showlegend=(col == 1)
                ),
                row=1, col=col
            )

        # Row 2: Source Coloring
        for label in ['Gigapath', 'Chief']:
            idx = source_labels == label
            color = dict_gigapath_or_chief[label] if (dict_gigapath_or_chief and label in dict_gigapath_or_chief) else None
            # print('2 reduced.shape', reduced.shape)
            # print('2 idx', len(idx))
            fig.add_trace(
                go.Scattergl(
                    x=reduced[idx, 0],
                    y=reduced[idx, 1],
                    mode='markers',
                    marker=dict(size=5, color=color),
                    name=f'Source: {label}',
                    legendgroup=f'Source_{label}',
                    showlegend=(col == 1)
                ),
                row=2, col=col
            )

        # Row 3: Source Coloring
        for label in ['ffpe', 'fresh']:
            idx = FFPE_or_Fresh_labels == label
            color = dict_ffpe_or_fresh[label] if (dict_ffpe_or_fresh and label in dict_ffpe_or_fresh) else None
            # print('3 reduced.shape', reduced.shape)
            # print('3 idx', idx)
            fig.add_trace(
                go.Scattergl(
                    x=reduced[idx, 0],
                    y=reduced[idx, 1],
                    mode='markers',
                    marker=dict(size=5, color=color),
                    name=f'Mode: {label}',
                    legendgroup=f'Mode_{label}',
                    showlegend=(col == 1)
                ),
                row=3, col=col
            )
    
    fig.update_layout(
        height=1000,
        width=1000,
        title_text=f'Embedding Visualization at Epoch {epoch}',
        legend=dict(
            itemsizing='constant',
            font=dict(size=10),
            traceorder='grouped'
        )
    )
    
    if save_dir != '':
        save_path = os.path.join(save_dir, f'embeddings_epoch_{epoch}.html')
        fig.write_html(save_path)
        print(f"Saved plot to {save_path}")
    # fig.show()

def plt_this_step(z1, z2, z1_giga_ffpe, z2_giga_fresh, batch_slide_id, ffpe_indices, fresh_indices, kept_indices,
                epoch, save_dir, color_dict_cancer,
                dict_id_type, dict_gigapath_or_chief, dict_ffpe_or_fresh):
    """
    Generate and save embedding plots during training.

    Parameters:
    - z1: embeddings from FFPE Gigapath
    - z1_giga_ffpe: fallback FFPE embeddings (matched to skipped)
    - z2_giga_fresh: matched fresh Chief embeddings
    - batch_slide_id: list of slide IDs from batch
    - ffpe_indices: indices in the batch corresponding to FFPE
    - fresh_indices: indices in the batch corresponding to fresh
    - epoch: current epoch number
    - save_dir: directory to save the plots
    - color_dict_cancer: dictionary mapping cancer types to colors
    - dict_id_type: maps TCGA ID to cancer type
    - dict_gigapath_or_chief: dict labeling source of z1/z2
    - dict_ffpe_or_fresh: dict labeling fresh or FFPE
    """
    slide_ids_clean = [
        re.match(r'(TCGA-\w\w-\w+)', sid).group(1) if sid.startswith('TCGA') else sid
        for sid in batch_slide_id
    ]

    cancer_types = [dict_id_type.get(sid) for sid in slide_ids_clean]
    
    # Concatenate FFPE + skipped FFPE
    z1_final = torch.cat([z1.cpu(), z1_giga_ffpe.cpu()], dim=0)
    z2_final = torch.cat([z2.cpu(), z2_giga_fresh.cpu()], dim=0)

    # Collect modes (cancer types) for all selected indices
    cancer_types_part1 = [cancer_types[i] for i in ffpe_indices]
    cancer_types_part2 = [cancer_types[i] for i in fresh_indices]
    
    cancer_types_part2 = [cancer_types_part2[i] for i in kept_indices]
    cancer_types_combined = cancer_types_part1 + cancer_types_part2

    modes_all = np.array(['ffpe'] * len(z1) + ['fresh'] * len(z1_giga_ffpe)).tolist()
    all_source_labels=np.array(['Chief'] * len(cancer_types_part1) + ['Gigapath'] * len(cancer_types_part2)).tolist()

    # Call the embedding plot function
    plot_embeddings_all(
        z1_final,
        z2_final,
        epoch + 1,
        cancer_types_combined,
        cancer_types_combined,
        color_dict_cancer=color_dict_cancer,
        all_modes=modes_all,
        all_source_labels=all_source_labels,
        save_dir=save_dir,
        dict_gigapath_or_chief=dict_gigapath_or_chief,
        dict_ffpe_or_fresh=dict_ffpe_or_fresh
    )

def plt_this_step_full(z1, z2, z1_giga_ffpe, z2_giga_fresh, batch_slide_id, ffpe_indices, fresh_indices, kept_indices,
                epoch, save_dir, color_dict_cancer,
                dict_id_type, dict_gigapath_or_chief, dict_ffpe_or_fresh, modes_all,all_source_labels):
    """
    Generate and save embedding plots during training.

    Parameters:
    - z1: embeddings from FFPE Gigapath
    - z1_giga_ffpe: fallback FFPE embeddings (matched to skipped)
    - z2_giga_fresh: matched fresh Chief embeddings
    - batch_slide_id: list of slide IDs from batch
    - ffpe_indices: indices in the batch corresponding to FFPE
    - fresh_indices: indices in the batch corresponding to fresh
    - epoch: current epoch number
    - save_dir: directory to save the plots
    - color_dict_cancer: dictionary mapping cancer types to colors
    - dict_id_type: maps TCGA ID to cancer type
    - dict_gigapath_or_chief: dict labeling source of z1/z2
    - dict_ffpe_or_fresh: dict labeling fresh or FFPE
    """

    # Extract slide IDs and cancer types
    slide_ids_clean = [
        re.match(r'(TCGA-\w\w-\w+)', sid).group(1) if sid.startswith('TCGA') else sid
        for sid in batch_slide_id]
    cancer_types = [dict_id_type.get(sid) for sid in slide_ids_clean]
    
    # Concatenate FFPE + skipped FFPE
    z1_final = torch.cat([z1.cpu(), z1_giga_ffpe.cpu()], dim=0)
    z2_final = torch.cat([z2.cpu(), z2_giga_fresh.cpu()], dim=0)

    plot_embeddings_all(
        z1_final,
        z2_final,
        epoch + 1,
        cancer_types,
        cancer_types,
        color_dict_cancer=color_dict_cancer,
        all_modes=modes_all,
        all_source_labels=all_source_labels,
        save_dir=save_dir,
        dict_gigapath_or_chief=dict_gigapath_or_chief,
        dict_ffpe_or_fresh=dict_ffpe_or_fresh
    )

