import numpy as np
import matplotlib.pyplot as plt

# http://localhost:2729/lab/tree/prov-gigapath/finetune/20240114_data_processing_for_Yuxing.ipynb
def normalize_attention(attn_values):
    """Normalize attention values to the range [0,1]."""
    min_attn = np.min(attn_values)
    max_attn = np.max(attn_values)
    return (attn_values - min_attn) / (max_attn - min_attn)


def compute_patch_size(coords, ratio):
    """
    Computes the patch size automatically based on the max range of coordinates and a given ratio.

    Args:
    - coords (numpy.ndarray): Array of shape (N, 2) containing x, y coordinates.
    - ratio (float): Ratio to determine patch size.

    Returns:
    - patch_size (int): Computed patch size.
    """
    x_range = np.max(coords[:, 0]) - np.min(coords[:, 0])
    y_range = np.max(coords[:, 1]) - np.min(coords[:, 1])
    max_range = max(x_range, y_range)
    
    patch_size = int(ratio * max_range)
    return max(patch_size, 1)  # Ensure patch size is at least 1

def downsample_with_auto_patch_size(data_dict, num_patches=100, #label=1, # invalid now
                                    patch_size=1000,
                                    #ratio=0.05,# invalid now
                                   ):
    """
    Downsamples the dictionary based on the specified label and automatically determines patch size.

    - If label == 1, use attention-based sampling with Gibbs sampling.
    - If label == 0, select patches randomly without considering attention.

    Patch size is automatically computed based on the coordinate range and ratio.

    Args:
    - data_dict (dict): Dictionary containing 'coords', 'features', and 'attn' keys.
    - num_patches (int): Number of patches to sample.
    - label (int): 1 for attention-based sampling, 0 for random patch selection.
    - ratio (float): Ratio of the max range to determine patch size.

    Returns:
    - downsampled_dict (dict): A dictionary with downsampled 'coords', 'features', and 'attn'.
    """
    label=data_dict['label']
    coords = data_dict['coords']
    features = data_dict['features']
    prob = normalize_attention(data_dict['attn'])  # Normalize attn values to [0,1]
    patch_size = patch_size# compute_patch_size(coords, ratio)

    selected_patches = []
    if label == 1:
        # Compute standard deviation of attention values
        # Adaptive threshold based on the mean and standard deviation of attention
        # threshold = np.mean(attn) + 0.5 * attn_std  
        threshold=0.5
        # print(f"Adaptive threshold for selection (normalized attn): {threshold:.4f}")
        time_of_sample=0
        for _ in range(num_patches):
            while True:

                # print('testing')
                # Randomly select a tile as the center
                center_idx = np.random.randint(0, len(coords))
                center_x, center_y = coords[center_idx]

                # Define the square patch
                patch_mask = (np.abs(coords[:, 0] - center_x) <= patch_size // 2) & \
                             (np.abs(coords[:, 1] - center_y) <= patch_size // 2)

                assert features.shape[0] == patch_mask.shape[0], "Mismatch in features and patch_mask dimensions! features{}, patch_mask{}, coords{}".format(features.shape[0],patch_mask.shape[0],coords.shape[0])

                
                # Extract patch data
                # print('patch_mask.shape',patch_mask.shape)
                # print('features.shape',features.shape)
                patch_coords = coords[patch_mask]
                patch_features = features[patch_mask]
                patch_prob = prob[patch_mask]
                patch_prob_std = np.std(patch_prob)

                # Compute average attention in the patch
                patch_avg_prob = np.mean(patch_prob)

                # Sample a value based on the patch attention and global std
                sampled_value = np.random.normal(loc=patch_avg_prob, scale=patch_prob_std)

                # Check if the sampled value exceeds the threshold
                # print('sampled_value {} threshold {}'.format(sampled_value, threshold))
                time_of_sample=time_of_sample+1

                if sampled_value > threshold:
                    selected_patches.append((patch_coords, patch_features, data_dict['attn'][patch_mask]))
                    break
                elif time_of_sample==20:
                    # Randomly select a tile as the center
                    center_idx = np.random.randint(0, len(coords))
                    center_x, center_y = coords[center_idx]
        
                    # Define the square patch
                    patch_mask = (np.abs(coords[:, 0] - center_x) <= patch_size // 2) & \
                                 (np.abs(coords[:, 1] - center_y) <= patch_size // 2)
        
                    # Extract patch data
                    patch_coords = coords[patch_mask]
                    patch_features = features[patch_mask]
                    patch_prob = data_dict['attn'][patch_mask]
        
                    selected_patches.append((patch_coords, patch_features, patch_prob))
                    break


    else:  # label == 0, random patch selection
        for _ in range(num_patches):
            # Randomly select a tile as the center
            center_idx = np.random.randint(0, len(coords))
            center_x, center_y = coords[center_idx]

            # Define the square patch
            patch_mask = (np.abs(coords[:, 0] - center_x) <= patch_size // 2) & \
                         (np.abs(coords[:, 1] - center_y) <= patch_size // 2)

            # Extract patch data
            
            patch_coords = coords[patch_mask]
            patch_features = features[patch_mask]
            patch_prob = data_dict['attn'][patch_mask]

            selected_patches.append((patch_coords, patch_features, patch_prob))

    # Aggregate results
    downsampled_coords = np.vstack([p[0] for p in selected_patches])
    downsampled_features = np.vstack([p[1] for p in selected_patches])
    downsampled_prob = np.hstack([p[2] for p in selected_patches])

    # Return downsampled dictionary
    downsampled_dict = {
        'coords': downsampled_coords,
        'features': downsampled_features,
        'attn': downsampled_prob,
        'label':data_dict['label']
    }

    return downsampled_dict