import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial


# Transcriptom decoder
class TranscriptomeNet(nn.Module):
    def __init__(self, 
                 input_dim, 
                 hidden_dim=512,
                 output_dim=None,
                 dropout=0.2,
                 norm_layer=partial(nn.LayerNorm, eps=1e-6)):

        super().__init__()
        
        self.fc1 = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            norm_layer(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        self.fc2 = nn.Sequential(
            nn.Linear(hidden_dim, input_dim),
            norm_layer(input_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        if output_dim is not None:
            self.classifier = nn.Linear(input_dim, output_dim)
        else:
            self.classifier = None

    def forward(self, x):
        x = self.fc1(x)  # [batch, input_dim] -> [batch, hidden_dim]
        x = self.fc2(x)  # [batch, hidden_dim] -> [batch, input_dim]
        
        if self.classifier is not None:
            x = self.classifier(x)  # [batch, input_dim] -> [batch, output_dim]
        return x


# Projector
class GigaOneLayer(nn.Module): ##### batch
    def __init__(self, model_path, layer_idx=11):
        """
        Load the model and extract the specified encoder layer.
        """
        super().__init__()  # ✅ must come first
        self.model_path = model_path
        self.layer_idx = layer_idx

        # Load full model and extract the target layer
        model = torch.load(model_path)
        self.layer = model.slide_encoder.encoder.layers[layer_idx]

        # Optional fixed parameters
        self.attn_mask = None
        self.rel_pos_bias = None
        self.multiway_split_position = None
        self.incremental_state = None
        self.this_head = None

    def forward(self, x_full):
        """
        Process a batch of inputs x_full: shape [n, tokens, 768]
        Returns: Tensor of shape [n, 768] (avg of non-CLS token outputs)
        """
        outputs = []

        for i in range(x_full.shape[0]):
            x = x_full[i:i+1]  # shape: [1, tokens, 768]
            x_cls = x[:, 0:1, :]  # CLS token

            encoder_padding_mask = None  # Add logic if needed

            # Pass through the layer
            x_output, l_aux_i, attn_prob_mtx_i, x_cls_out, l_aux_i_cls, attn_prob_mtx_i_cls = self.layer(
                x,
                x_cls,
                layer_n=self.layer_idx,
                this_head=self.this_head,
                encoder_padding_mask=encoder_padding_mask if self.incremental_state is None else None,
                attn_mask=self.attn_mask,
                rel_pos=self.rel_pos_bias,
                multiway_split_position=self.multiway_split_position,
                incremental_state=self.incremental_state[self.layer_idx] if self.incremental_state is not None else None,
                coords=None
            )

            # Average over non-CLS tokens
            x_avg = torch.mean(x_output[:, 1:, :], dim=1)  # shape: [1, 768]
            outputs.append(x_avg)

        # Stack into final tensor [n, 768]
        x_full_fin = torch.cat(outputs, dim=0)
        return x_full_fin



class Projector(nn.Module):
    def __init__(self, input_dim=768, output_dim=768, num_layers=2, hidden_dim=None, dropout=0.0):
        super().__init__()
        if hidden_dim is None:
            hidden_dim = input_dim  # input_dim = embedding_dim_chief = 768
        
        layers = []
        # First layer
        layers.append(nn.Linear(input_dim, hidden_dim))
        # layers.append(nn.ReLU())
        if dropout > 0:
            layers.append(nn.Dropout(dropout))
        
        # Hidden layers
        for _ in range(num_layers - 2):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            # layers.append(nn.ReLU())
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
        
        # Last layer
        layers.append(nn.Linear(hidden_dim, output_dim)) # output_dim = projected_dim = 768
        
        self.net = nn.Sequential(*layers)

    def forward(self, x):
        return self.net(x)



class OneLayerTransformer(nn.Module):
    def __init__(self, embed_dim=768, nhead=16, dropout=0.1,
                 norm=False, norm_eps=1e-5, freeze_except_ffn_outproj=False):
        super().__init__()

        # 初始化一个 Transformer Encoder Layer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=nhead,
            dropout=dropout,
            batch_first=True
        )
        
        # 只训练 FFN 和 out_proj，如果设定了该参数
        if freeze_except_ffn_outproj:
            for name, param in encoder_layer.named_parameters():
                if not (
                    # 'linear1' in name or
                    # 'linear2' in name or
                    'self_attn.out_proj' in name # only train self_attn.out_proj
                ):
                    param.requires_grad = False

        # 组装为 Encoder
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=1)

        self.norm = norm
        if norm:
            self.layer_norm = nn.LayerNorm(embed_dim, eps=norm_eps)

    def forward(self, x, attention_mask):
        # x: [B, T, 768], attention_mask: [B, T] (1 for valid, 0 for pad)
        src_key_padding_mask = ~attention_mask.bool()

        x = self.transformer_encoder(x, src_key_padding_mask=src_key_padding_mask)
        x = x[:, 1:, :]  # drop the first token (e.g., CLS)

        src_key_padding_mask = src_key_padding_mask[:, 1:]
        attention_mask = attention_mask[:, 1:]

        if self.norm:
            x = self.layer_norm(x)

        attention_mask = attention_mask.unsqueeze(-1)  # [B, T-1, 1]
        x_masked = x * attention_mask  # [B, T-1, 768]
        summed = x_masked.sum(dim=1)  # [B, 768]
        counts = attention_mask.sum(dim=1)  # [B, 1]
        averaged = summed / counts.clamp(min=1)  # [B, 768]

        return averaged

