import torch
import random
from torch.utils.data import Dataset




# Dataset
class SlideEmbeddingDataset(Dataset):
    def __init__(self, x, mask, slide_ids, df_embeddings):
        self.x = x  # Tensor: [N, 9250, 768]
        self.mask = mask  # Tensor: [N, 9250]
        self.slide_ids = slide_ids  # List of strings
        self.embeddings = df_embeddings  # Tensor: [N, 768]

    def __len__(self):
        return self.x.shape[0]

    def __getitem__(self, idx):
        return {
            'x': self.x[idx],
            'mask': self.mask[idx],
            'slide_id': self.slide_ids[idx],
            'embedding': self.embeddings[idx]
        }

class SlideEmbeddingWithModeDataset(Dataset):
    def __init__(self, base_dataset, mode):
        self.base_dataset = base_dataset
        self.mode = mode

    def __len__(self):
        return len(self.base_dataset)

    def __getitem__(self, idx):
        sample = self.base_dataset[idx]
        sample['mode'] = self.mode
        return sample


# FFPE --> find FF
def get_random_batch_by_prefix_list(dataset, full_slide_ids):
    # print('len(full_slide_ids)',len(full_slide_ids))
    prefixes = [sid[:12] for sid in full_slide_ids]
    
    selected_x, selected_mask, selected_embedding, selected_slide_id = [], [], [], []
    keep_indices = []

    for i, prefix in enumerate(prefixes):
        matched_indices = [j for j, sid in enumerate(dataset.slide_ids) if sid.startswith(prefix)]
        if not matched_indices:
            continue
        keep_indices.append(i) #actually it's kept
        idx = random.choice(matched_indices)
        sample = dataset[idx]
        selected_x.append(sample['x'].unsqueeze(0))  # add batch dim
        selected_mask.append(sample['mask'].unsqueeze(0))
        selected_embedding.append(sample['embedding'].unsqueeze(0))
        selected_slide_id.append(sample['slide_id'])

    if selected_x:
        x_batch = torch.cat(selected_x, dim=0)
        mask_batch = torch.cat(selected_mask, dim=0)
        embedding_batch = torch.cat(selected_embedding, dim=0)
    else:
        x_batch = mask_batch = embedding_batch = None

    return {
        'x': x_batch,
        'mask': mask_batch,
        'embedding': embedding_batch,
        'slide_id': selected_slide_id,
        'keep_indices': keep_indices  # indices into full_slide_ids that failed
    }

def refresh_counter(recount_dict, epoch):
    recount_dict['four'] += 1
    if recount_dict['four'] >= 4:
        recount_dict['four'] = 0

    recount_dict['eight'] += 1
    if recount_dict['eight'] >= 8:
        recount_dict['eight'] = 0

    print(recount_dict)
    return recount_dict

