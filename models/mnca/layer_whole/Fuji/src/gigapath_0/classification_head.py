import torch
from torch import nn
from . import slide_encoder
from src.models_mae import MaskedAutoencoderViT
from functools import partial
import os
from datetime import datetime
from src._util import save_latent_embedding
from src.models import TranscriptomeNet
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path


def reshape_input(imgs, coords, pad_mask=None):
    if len(imgs.shape) == 4:
        imgs = imgs.squeeze(0)
    if len(coords.shape) == 4:
        coords = coords.squeeze(0)
    if pad_mask is not None:
        if len(pad_mask.shape) != 2:
            pad_mask = pad_mask.squeeze(0)
    return imgs, coords, pad_mask


class ClassificationHead(nn.Module):  #### model from customize_fun/train.py
    """
    The classification head for the slide encoder

    Arguments:
    ----------
    input_dim: int
        The input dimension of the slide encoder
    latent_dim: int
        The latent dimension of the slide encoder
    feat_layer: str
        The layers from which embeddings are fed to the classifier, e.g., 5-11 for taking out the 5th and 11th layers
    n_classes: int
        The number of classes
    model_arch: str
        The architecture of the slide encoder
    pretrained: str
        The path to the pretrained slide encoder
    freeze: bool
        Whether to freeze the pretrained model
    """

    def __init__(
        self,
        input_dim,
        latent_dim,
        feat_layer,
        n_classes=2,
        model_arch="gigapath_slide_enc12l768d", # LongNetViT register_model
        pretrained="hf_hub:prov-gigapath/prov-gigapath",
        freeze=False,
        n_heads=50,
        **kwargs,
    ):
        super(ClassificationHead, self).__init__()

        # setup the slide encoder
        self.feat_layer = [eval(x) for x in feat_layer.split("-")]
        self.feat_dim = len(self.feat_layer) * latent_dim
        self.slide_encoder = slide_encoder.create_model(pretrained, model_arch, in_chans=input_dim, **kwargs)

        # whether to freeze the pretrained model
        if freeze:
            print("Freezing Pretrained GigaPath model")
            for name, param in self.slide_encoder.named_parameters():
                param.requires_grad = False
            print("Done")
        # setup the classifier
        self.classifier = nn.Sequential(*[nn.Linear(self.feat_dim, n_classes)]) # not used, but saved structure in .pth
        self.classifier2 = nn.Sequential(*[nn.Linear(self.feat_dim, n_classes)]) # not used, but saved structure in .pth

        
        # mutation signature loss 2
        # self.classifier_head0 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head1 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head2 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head3 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head4 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head5 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head6 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head7 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head8 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head9 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head10 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head11 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head12 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head13 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head14 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head15 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head16 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head17 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head18 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head19 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head20 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head21 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head22 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head23 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head24 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head25 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head26 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head27 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head28 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head29 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head30 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head31 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head32 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head33 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head34 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head35 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head36 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head37 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head38 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head39 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head40 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head41 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head42 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head43 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head44 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head45 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head46 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head47 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head48 = nn.Sequential(*[nn.Linear(48, n_classes)])
        # self.classifier_head49 = nn.Sequential(*[nn.Linear(48, n_classes)])
        self.classifier_heads = nn.ModuleList([nn.Sequential(nn.Linear(48, n_classes)) for _ in range(50)])
        
        decoder_embed_dim=768
        self.mae_decoder = MaskedAutoencoderViT(patch_size=16, embed_dim=decoder_embed_dim, depth=12, num_heads=12, # mae decoder for reconstruct loss
                                        decoder_embed_dim=decoder_embed_dim, decoder_depth=8, decoder_num_heads=16,
                                        mlp_ratio=4, norm_layer=partial(nn.LayerNorm, eps=1e-6))
        
        self.trans_decoder = TranscriptomeNet(input_dim=decoder_embed_dim, hidden_dim=1024) # transcriptom decoder for mae loss
        
        
    def forward(self, 
                images: torch.Tensor, 
                coords: torch.Tensor,
                args, 
                attn, 
                images_full=None, img_coords_full=None,
                this_head=None) -> torch.Tensor:
        """
        Arguments:
        ----------
        images: torch.Tensor
            The input images with shape [N, L, D]
        coords: torch.Tensor
            The input coordinates with shape [N, L, 2]
        """
        if len(images.shape) == 2:
            images = images.unsqueeze(0)
        assert len(images.shape) == 3
        
        # forward GigaPath slide encoder
        if (args.w2==1) or (args.w3==1) or (args.w5==1):
            img_enc, full_tile_no_cls, full_tile_qkv, outcomes_cls, attn_prob_mtx, x_list_cls = self.slide_encoder.forward(images, coords, this_head, args, all_layer_embed=True) # attn_prob_mtx for plot
            #### full_tile_no_cls: all layer embeddings with 14 variable, first variable is the input, others corresponding to 13 layers
            if args.save_latent_emb_path !='':
                save_latent_embedding(args, this_head, img_enc)
        
        # port
        tile_embedding = full_tile_no_cls[-2] # layer 12 embedding
        tile_qkv = full_tile_qkv
        
        # spot transcriptom #### 
        '''
        args.slide_id (TCGA-ZN-A9VP) added in train.py, match st
        projector pytorch 2 layer
        '''
        if args.w5 ==1:
            st_embedding = self.trans_decoder(full_tile_no_cls[-2])
            seq_emb = pd.read_csv(args.seq_emb)
            st_target = torch.load(seq_emb[seq_emb['ID'] == args.slide_id[:12]]['Path'])
            mae_loss = nn.L1Loss()  
            st_loss = mae_loss(st_embedding, st_target) 
        

        # classifier loss 1
        if args.w1==1:
            img_enc = [img_enc[i] for i in self.feat_layer]
            img_enc = torch.cat(img_enc, dim=-1)
            h = img_enc.reshape([-1, img_enc.size(-1)])
            clasification_loss = self.classifier(h)

        # loss2 and loss3
        if (args.w2==1) or (args.w3==1):
            # ms loss 2
            '''
            if outcomes_cls[-1].shape[2]==48:
                if this_head==0:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head0(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==1:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head1(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==2:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head2(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==3:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head3(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==4:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head4(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==5:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head5(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==6:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head6(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==7:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head7(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==8:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head8(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==9:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head9(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==10:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head10(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==11:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head11(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==12:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head12(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==13:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head13(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==14:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head14(outcomes_cls[-1][:,0:1,:].squeeze(1))  # 0-14 used, corresponding to 15 heads
                if this_head==15:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head15(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==16:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head16(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==17:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head17(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==18:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head18(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==19:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head19(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==20:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head20(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==21:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head21(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==22:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head22(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==23:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head23(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==24:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head24(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==25:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head25(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==26:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head26(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==27:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head27(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==28:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head28(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==29:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head29(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==30:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head30(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==31:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head31(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==32:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head32(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==33:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head33(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==34:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head34(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==35:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head35(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==36:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head36(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==37:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head37(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==38:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head38(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==39:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head39(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==40:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head40(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==41:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head41(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==42:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head42(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==43:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head43(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==44:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head44(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==45:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head45(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==46:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head46(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==47:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head47(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==48:
                    # print('multihead classifier_head0')
                    ms_loss = self.classifier_head48(outcomes_cls[-1][:,0:1,:].squeeze(1))
                if this_head==49:
                    # print('multihead classifier_head1')
                    ms_loss = self.classifier_head49(outcomes_cls[-1][:,0:1,:].squeeze(1))
            else:
                ms_loss = self.classifier2(outcomes_cls[-1][:,0:1,:].squeeze(1))
            '''
            if outcomes_cls[-1].shape[2] == 48:
                if 0 <= this_head < 50:  # make sure this_head exist
                    ms_loss = self.classifier_heads[this_head](outcomes_cls[-1][:, 0:1, :].squeeze(1))
                else:
                    raise ValueError(f"Invalid this_head: {this_head}. Expected 0-49.")
            else:
                ms_loss = self.classifier2(outcomes_cls[-1][:, 0:1, :].squeeze(1))
                
            # attention loss 3
            attn_prob = attn.squeeze(0) / attn.sum()
            target_prob = attn_prob_mtx[-1][0, this_head, 0, 1:] / attn_prob_mtx[-1][0, this_head, 0, 1:].sum() # when attn is 0, target is randomly assigned
            attn_loss = torch.nn.functional.kl_div(torch.log(attn_prob), target_prob, reduction='batchmean')
            print('attn_prob:', attn_prob) # tile level cls attention value
            print('target_prob:', target_prob) # tile level attention value
            print('attn_prob:', attn_prob.shape)
            print('target_prob:', target_prob.shape)
    
    
        # reconstruct loss 4
        if args.w4==1: 
            # dinov2 filtered 25 for predict
            images_filtered, coords_filtered, mask, ids_restore = \
                self.mae_decoder.extract_msk_ids_restore(embedding=torch.concat([images,coords],axis=2), mask_ratio=0.75) 

            # encoder, gigapath encoder modified
            img_enc_filtered, outcomes_filtered_full_tile_no_cls_output, outcomes_filtered_cls, attn_filtered_prob_mtx, x_list_filtered_cls = \
                self.slide_encoder.forward(images_filtered.to(images.dtype), 
                                                            coords_filtered.to(coords.dtype), 
                                                            this_head, 
                                                            args, 
                                                            all_layer_embed=True)
            # decoder, MAE decoder modified
            pred = self.mae_decoder.forward_decoder_customize(outcomes_filtered_full_tile_no_cls_output[-2], 
                                                            coords.to(coords.dtype), 
                                                            ids_restore)
            # loss
            reconstruct_loss = self.mae_decoder.loss_customize(pred=pred, 
                                                            target=images.to(coords.dtype), 
                                                            mask=mask)
            
            # reconstruct related variables
            all_var_reccon=(images, coords, images_filtered, coords_filtered, coords_filtered, mask, ids_restore, \
                        img_enc_filtered, outcomes_filtered_full_tile_no_cls_output, outcomes_filtered_cls, attn_filtered_prob_mtx,x_list_filtered_cls, \
                        pred, images.to(coords.dtype), reconstruct_loss)

            # save for visualization
            if args.save_emb !='':
                print('args.save_emb:',args.save_emb)
                file_name=args.save_emb+str(args.epoch)+'_'+args.slide_id[0]+'_'+'recon.pth'
                torch.save(all_var_reccon, file_name)
        else:
            reconstruct_loss=torch.tensor(0)
            all_var_reccon = None 


        if args.plt_attn_compare:
            # Compute vmax for the first plot
            vmax_one_mtx = np.percentile(attn.cpu(), 90)
            
            # Create a figure with 1 row and 2 columns
            fig, axes = plt.subplots(1, 2, figsize=(12, 5))  # Adjust figsize as needed
            
            # First scatter plot
            sc1 = axes[0].scatter(coords[0,:,0].cpu(), 
                                coords[0,:,1].cpu(), 
                                c=attn.cpu(), 
                                s=3,
                                vmax=vmax_one_mtx)
            
            fig.colorbar(sc1, ax=axes[0])  # Add colorbar
            axes[0].set_title("Attention")
            
            # Compute vmax for the second plot
            vmax_one_mtx = np.percentile(attn_prob_mtx[-1][0, this_head, 0,1:].cpu().detach().numpy(), 90)

            # Second scatter plot
            sc2 = axes[1].scatter(coords[0,:,0].cpu(), 
                                coords[0,:,1].cpu(), 
                                c=attn_prob_mtx[-1][0, this_head, 0, 1:].cpu().detach().numpy(), 
                                s=3,
                                vmax=vmax_one_mtx)
            fig.colorbar(sc2, ax=axes[1])  # Add colorbar
            axes[1].set_title("Attention Probability")
            # Save plot with datetime
            save_dir = 'results/attention/plot_results'
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
            os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
            save_path = os.path.join(save_dir, f'Attention_Probability_{timestamp}.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved plot to {save_path}")
            plt.close() 


        if (args.w2==1) or (args.w3==1) or (args.w5==1):
            return clasification_loss, ms_loss, attn_prob_mtx, outcomes_cls[-1].squeeze(1), attn_loss, reconstruct_loss, st_loss, all_var_reccon
        elif args.w1==1:
            ms_loss=None
            attn_prob_mtx=None
            st_loss=None
            attn_loss=None
            st_loss=None
            return clasification_loss, ms_loss, attn_prob_mtx, st_loss, attn_loss, reconstruct_loss, st_loss, all_var_reccon
        else:
            clasification_loss=None
            ms_loss=None
            attn_prob_mtx=None
            st_loss=None
            attn_loss=None
            st_loss=None
            return clasification_loss, ms_loss, attn_prob_mtx, st_loss, attn_loss, reconstruct_loss, st_loss, all_var_reccon



def get_model(**kwargs):
    # default model is set to 'classification' if not provided
    task_mode = kwargs.get("task_mode", "classification")

    if task_mode == 'classification':
        model = ClassificationHead(**kwargs)
    else:
        raise ValueError(f"Invalid task_mode: {task_mode}. Expected 'classification' or 'get_cls'.")

    return model
