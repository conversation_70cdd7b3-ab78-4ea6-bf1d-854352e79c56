import torch
import torch.nn.functional as F
import pandas as pd
import os



def scaled_dot_product_attention_flash(q, k, v, is_causal=False, softmax_scale=None, dropout=0.0, mask=None):
    """
    Scaled dot-product attention with additional parameters to mimic Flash Attention behavior.

    Args:
        q: Query tensor of shape [batch_size, seq_length, num_heads, head_dim]
        k: Key tensor of shape [batch_size, seq_length, num_heads, head_dim]
        v: Value tensor of shape [batch_size, seq_length, num_heads, head_dim]
        is_causal: If True, applies a causal mask to ensure no information "leaks" from future positions.
        softmax_scale: Optional scaling factor for softmax (default is 1/sqrt(head_dim)).
        dropout: Dropout rate to apply to attention weights (default is 0.0).
        mask: Optional mask tensor of shape [batch_size, seq_length, seq_length].

    Returns:
        output: Final output tensor after attention, shape [batch_size, seq_length, num_heads, head_dim]
        attention_scores: Attention score matrix, shape [batch_size, seq_length, seq_length, num_heads]
    """
    batch_size, seq_length, num_heads, head_dim = q.shape

    # Step 1: Compute the dot product between q and k
    attention_logits = torch.einsum('bqnh,bknh->bqkn', q, k)

    # Step 2: Apply softmax scaling (default is 1/sqrt(head_dim) if not provided)
    if softmax_scale is None:
        softmax_scale = 1.0 / torch.sqrt(torch.tensor(head_dim, dtype=torch.float32))
    attention_logits *= softmax_scale

    # Step 3: Apply causal mask if needed
    if is_causal:
        causal_mask = torch.triu(torch.ones(seq_length, seq_length, device=q.device), diagonal=1).bool()
        attention_logits.masked_fill_(causal_mask[None, :, :, None], float('-inf'))

    # Step 4: Apply additional mask if provided (e.g., padding mask)
    if mask is not None:
        attention_logits += (mask * -1e9)

    # Step 5: Compute attention weights with softmax
    attention_scores = F.softmax(attention_logits, dim=-2)

    # Step 6: Apply dropout to attention scores if dropout > 0
    if dropout > 0.0:
        attention_scores = F.dropout(attention_scores, p=dropout, training=True)

    # Step 7: Compute the weighted sum of values (v)
    output = torch.einsum('bqkn,bknh->bqnh', attention_scores, v)

    return output, attention_scores



def scaled_dot_product_attention_longnet(qi, ki, vi, is_causal=False, softmax_scale=None, dropout=0.0, mask=None):
    """
    Scaled dot-product attention adapted for LongNet, where q, k, and v have shapes [batch_size, seq_length, head_dim].

    Args:
        qi: Query tensor of shape [batch_size, seq_length, head_dim]
        ki: Key tensor of shape [batch_size, seq_length, head_dim]
        vi: Value tensor of shape [batch_size, seq_length, head_dim]
        is_causal: If True, applies a causal mask to ensure no information "leaks" from future positions.
        softmax_scale: Optional scaling factor for softmax (default is 1/sqrt(head_dim)).
        dropout: Dropout rate to apply to attention weights (default is 0.0).
        mask: Optional mask tensor of shape [batch_size, seq_length, seq_length].

    Returns:
        output: Final output tensor after attention, shape [batch_size, seq_length, head_dim]
        attention_scores: Attention score matrix, shape [batch_size, seq_length, seq_length]
    """
    batch_size, seq_length, head_dim = qi.shape

    # Step 1: Compute the dot product between qi and ki
    attention_logits = torch.bmm(qi, ki.transpose(1, 2))  # Shape: [batch_size, seq_length, seq_length]

    # Step 2: Apply softmax scaling (default is 1/sqrt(head_dim) if not provided)
    if softmax_scale is None:
        softmax_scale = 1.0 / torch.sqrt(torch.tensor(head_dim, dtype=torch.float32, device=qi.device))
    attention_logits *= softmax_scale

    # Step 3: Apply causal mask if needed
    if is_causal:
        causal_mask = torch.triu(torch.ones(seq_length, seq_length, device=qi.device), diagonal=1).bool()
        attention_logits.masked_fill_(causal_mask[None, :, :], float('-inf'))

    # Step 4: Apply additional mask if provided (e.g., padding mask)
    if mask is not None:
        attention_logits += (mask * -1e9)

    # Step 5: Compute attention weights with softmax
    attention_scores = F.softmax(attention_logits, dim=-1)  # Shape: [batch_size, seq_length, seq_length]

    # Step 6: Apply dropout to attention scores if dropout > 0
    if dropout > 0.0:
        attention_scores = F.dropout(attention_scores, p=dropout, training=True)

    # Step 7: Compute the weighted sum of values (vi)
    output = torch.bmm(attention_scores, vi)  # Shape: [batch_size, seq_length, head_dim]

    return output, attention_scores



def standard_attention(query, key, value, mask=None, is_causal=False, softmax_scale=None, dropout=None):
    """
    Compute standard attention with additional parameters.

    Args:
        query (torch.Tensor): Query matrix of shape (batch_size, seq_len, dim).
        key (torch.Tensor): Key matrix of shape (batch_size, seq_len, dim).
        value (torch.Tensor): Value matrix of shape (batch_size, seq_len, dim).
        mask (torch.Tensor, optional): Mask tensor of shape (batch_size, seq_len, seq_len).
        is_causal (bool, optional): If True, apply causal masking. Default: False.
        softmax_scale (float, optional): Scaling factor for softmax. If None, defaults to 1 / sqrt(dim).
        dropout (float, optional): Dropout probability for attention weights. Default: None.

    Returns:
        torch.Tensor: Output of the attention mechanism.
        torch.Tensor: Attention weights.
    """
    # Scale query by softmax_scale or default to 1 / sqrt(dim)
    d_k = query.size(-1)
    scale = softmax_scale if softmax_scale is not None else 1.0 / torch.sqrt(torch.tensor(d_k, dtype=torch.float32))
    scores = torch.matmul(query, key.transpose(-2, -1)) * scale

    # Apply mask (if any)
    if mask is not None:
        scores = scores.masked_fill(mask == 0, float('-inf'))

    # Apply causal masking (if enabled)
    if is_causal:
        seq_len = query.size(1)
        causal_mask = torch.triu(torch.ones(seq_len, seq_len, device=query.device), diagonal=1).bool()
        scores = scores.masked_fill(causal_mask, float('-inf'))

    # Apply softmax to get attention weights
    attention_weights = F.softmax(scores, dim=-1)

    # Apply dropout to attention weights (if specified)
    if dropout is not None:
        attention_weights = F.dropout(attention_weights, p=dropout, training=True)

    # Multiply attention weights with the value
    output = torch.matmul(attention_weights, value)

    return output, attention_weights


def reverse_dictionary(my_dict):

    return {value: key for key, value in my_dict.items()}



def save_latent_embedding(args, this_head, img_enc):
    # Extract values

    print('img_enc[-2].shape',img_enc[-2].shape) # current size torch.Size([3, 768]) # batch size is 3
    slide_id_val = args.slide_id[0] # args.slide_id is a list of >1 element so i cannot use [0]
    # print()
    sig_type_val = reverse_dictionary(args.sig_type_head_dict)[this_head]

    label_val = args.label.squeeze().item()  # extract the number from tensor([[0]])
    embedding = img_enc[-2].squeeze(0).tolist()  # convert tensor of shape (1,768) to a list of 768 values

    # Build a dictionary for the one-row DataFrame

    row_data = {
        'slide_id': slide_id_val,
        'sig_type': sig_type_val,
        'label': label_val,
    }

    # Add each embedding value as a separate column (emb_0, emb_1, ..., emb_767)
    for i, val in enumerate(embedding):
        row_data[f'emb_{i}'] = val

    # Create a DataFrame with one row
    df = pd.DataFrame([row_data])

    # Save to CSV: append if file exists, no header/index if appending
    # print('saving')
    if os.path.exists(args.save_latent_emb_path):
        df.to_csv(args.save_latent_emb_path, mode='a', header=False, index=False)
    else:
        df.to_csv(args.save_latent_emb_path, mode='w', header=True, index=False)





# def reverse_dictionary(d):
#     return {v: k for k, v in d.items()}

# def save_latent_embedding(args, this_head, img_enc):
#     """
#     Saves a batch of latent embeddings into a CSV file.
#     """
#     print('img_enc[-2].shape', img_enc[-2].shape)  # e.g., torch.Size([3, 768])
#     print('args.label', args.label)  # e.g., torch.Size([3, 768])
#     embeddings = img_enc[-2]  # shape: [B, 768]
#     print('embeddings.shape',embeddings.shape[0])
#     print('this_head',this_head)
#     print('img_enc[-1].shape',img_enc[-1].shape)
#     batch_size = embeddings.shape[0]
    
#     # Make sure this_head is a list of length equal to batch_size
#     assert len(this_head) == batch_size, f"this_head length {len(this_head)} != batch_size {batch_size}"

#     # Prepare list of DataFrame rows
#     rows = []
#     for i in range(batch_size):
#         slide_id_val = args.slide_id[i]  # Assume args.slide_id is a list with B elements
#         sig_type_val = reverse_dictionary(args.sig_type_head_dict)[this_head[i]]
#         label_val = args.label[i].item() if isinstance(args.label, torch.Tensor) else args.label[i]
#         embedding = embeddings[i].tolist()

#         row_data = {
#             'slide_id': slide_id_val,
#             'sig_type': sig_type_val,
#             'label': label_val,
#         }
#         row_data.update({f'emb_{j}': val for j, val in enumerate(embedding)})

#         rows.append(row_data)

#     # Create DataFrame
#     df = pd.DataFrame(rows)

#     # Save to CSV
#     if os.path.exists(args.save_latent_emb_path):
#         df.to_csv(args.save_latent_emb_path, mode='a', header=False, index=False)
#     else:
#         df.to_csv(args.save_latent_emb_path, mode='w', header=True, index=False)