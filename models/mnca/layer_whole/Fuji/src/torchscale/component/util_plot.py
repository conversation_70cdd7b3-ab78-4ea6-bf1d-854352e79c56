################################################
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import os
from datetime import datetime

import numpy as np
import torch
import matplotlib.pyplot as plt
import seaborn as sns

import matplotlib.pyplot as plt
import numpy as np
import torch



def plot_qkvi_heatmaps(qi, ki, vi):
    tensors = {'qi': qi, 'ki': ki, 'vi': vi}
    
    for name, tensor in tensors.items():
        # Average across the first dimension (batch size of 16)
        averaged_tensor = tensor.mean(dim=0)  # Shape: [373, 48]

        plt.figure(figsize=(30, 30))
        sns.heatmap(averaged_tensor.detach().cpu().numpy(), cmap='viridis')
        plt.title(f'{name} - Averaged Heatmap')
        plt.xlabel('Features')
        plt.ylabel('Samples')
        # Save plot with datetime
        save_dir = 'results/attention/plot_results'
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
        os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
        save_path = os.path.join(save_dir, f'Averaged_Heatmaps_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Saved plot to {save_path}")
        plt.close() 



def plot_attention_heatmaps_onerow(attn_prob_mtx, 
                            coords, 
                            attn_prob_mtx_cls=None,
                            cmap='viridis'
                            ):
    # Extract the first attention matrix from the batch of 16
    first_dim_mtx = attn_prob_mtx[0, 0]  # Shape: [769, 769]
    first_dim_array = first_dim_mtx.detach().cpu().numpy() if torch.is_tensor(first_dim_mtx) else first_dim_mtx
    vmax_first = np.percentile(first_dim_array, 95)


    # Average across the 16 attention matrices
    averaged_mtx = attn_prob_mtx.mean(1)[0]  # Shape: [769, 769]
    averaged_mtx_cls = attn_prob_mtx_cls.mean(1)[0] if attn_prob_mtx_cls is not None else None
    
    averaged_array = averaged_mtx.detach().cpu().numpy() if torch.is_tensor(averaged_mtx) else averaged_mtx
    averaged_array_cls = averaged_mtx_cls.detach().cpu().numpy() if attn_prob_mtx_cls is not None and torch.is_tensor(averaged_mtx_cls) else averaged_mtx_cls
    
    vmax_avg = np.percentile(averaged_array, 90)
    vmax_avg_cls = np.percentile(averaged_array_cls, 90) if averaged_array_cls is not None else None
    
    # Prepare coordinate array
    coords_array = coords.squeeze(0).detach().cpu().numpy() if torch.is_tensor(coords) else coords.squeeze(0)

    fig, axes = plt.subplots(5, 4, figsize=(12, 16))  # Create a 4x4 subplot grid
    axes = axes.flatten()  # Flatten axes array for easy indexing

    # print('util_plot.py attn_prob_mtx',attn_prob_mtx)
    for head_no in range(20):
        one_mtx = attn_prob_mtx[:, head_no, 0, 1:][0]
        one_mtx_array = one_mtx.detach().cpu().numpy() if torch.is_tensor(one_mtx) else one_mtx
        vmax_one_mtx = np.percentile(one_mtx_array, 90)
        
        ax = axes[head_no]  # Select the corresponding subplot
        scatter = ax.scatter(
            coords_array[:, 0], 
            coords_array[:, 1], 
            c=one_mtx_array, 
            cmap=cmap, 
            s=1, 
            vmax=vmax_one_mtx, 
            marker='s'
        )
        fig.colorbar(scatter, ax=ax)  # Add colorbar to each subplot
        ax.set_title(f'Head {head_no}')  # Title for each subplot
        if attn_prob_mtx.shape[1]-1==head_no:
            break # adapt for layers 0-11; layer 12 has 20 heads by default
    plt.tight_layout()  # Adjust layout to prevent overlap
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'attn_prob_mtx_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 

    
    # Define figure with three subplots in one row
    fig, axs = plt.subplots(1, 3, figsize=(18, 6))  # 3 columns in one row

    # Titles for the three plots
    cls_titles = ['row', 'col', 'cls']

    # First plot (row)
    vmax_avg_row = np.percentile(averaged_array[0, 1:], 90)
    sc = axs[0].scatter(coords_array[:, 0], coords_array[:, 1], 
                        c=averaged_array[0, 1:], cmap=cmap, s=3, vmax=vmax_avg_row, marker='s')
    axs[0].set_title(cls_titles[0])
    axs[0].set_xlabel('X Coordinate')
    axs[0].set_ylabel('Y Coordinate')
    axs[0].set_aspect('equal')
    fig.colorbar(sc, ax=axs[0])  # Add colorbar

    # Second plot (col)
    vmax_avg_col = np.percentile(averaged_array[1:, 0], 90)
    sc = axs[1].scatter(coords_array[:, 0], coords_array[:, 1], 
                        c=averaged_array[1:, 0], cmap=cmap, s=3, vmax=vmax_avg_col, marker='s')
    axs[1].set_title(cls_titles[1])
    axs[1].set_xlabel('X Coordinate')
    axs[1].set_ylabel('Y Coordinate')
    axs[1].set_aspect('equal')
    fig.colorbar(sc, ax=axs[1])  # Add colorbar

    # Third plot (cls) - Only if attn_prob_mtx_cls is provided
    if attn_prob_mtx_cls is not None:
        sc = axs[2].scatter(coords_array[:, 0], coords_array[:, 1], 
                            c=averaged_array_cls[0, :], cmap=cmap, s=3, vmax=vmax_avg_cls, marker='s')
        axs[2].set_title(cls_titles[2])
        axs[2].set_xlabel('X Coordinate')
        axs[2].set_ylabel('Y Coordinate')
        axs[2].set_aspect('equal')
        fig.colorbar(sc, ax=axs[2])  # Add colorbar
    else:
        axs[2].remove()  # Remove third subplot if cls data is not available

    # Adjust layout and show the combined figure
    fig.suptitle('Attention Heatmaps', fontsize=16)
    plt.tight_layout()
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'Attention_Heatmaps_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 


def plot_attention_heatmaps(attn_prob_mtx, 
                            coords, 
                            attn_prob_mtx_cls=None,
                            cmap='rainbow'):
    # 1. Extract the first attention matrix from the batch of 16
    first_dim_mtx = attn_prob_mtx[0, 0]  # Shape: [769, 769]
    first_dim_array = first_dim_mtx.detach().cpu().numpy() if torch.is_tensor(first_dim_mtx) else first_dim_mtx
    vmax_first = np.percentile(first_dim_array, 95)
    
    # 2. Average across the 16 attention matrices
    averaged_mtx = attn_prob_mtx.mean(1)[0]  # Shape: [769, 769]
    averaged_mtx_cls = attn_prob_mtx_cls.mean(1)[0] if attn_prob_mtx_cls is not None else None
    
    averaged_array = averaged_mtx.detach().cpu().numpy() if torch.is_tensor(averaged_mtx) else averaged_mtx
    averaged_array_cls = averaged_mtx_cls.detach().cpu().numpy() if attn_prob_mtx_cls is not None and torch.is_tensor(averaged_mtx_cls) else averaged_mtx_cls
    
    vmax_avg = np.percentile(averaged_array, 90)
    vmax_avg_cls = np.percentile(averaged_array_cls, 90) if averaged_array_cls is not None else None

    # 3. Plotting coords with attention probabilities
    coords_array = coords.squeeze(0).detach().cpu().numpy() if torch.is_tensor(coords) else coords.squeeze(0)

    range_val = 1  # Adjustable range
    fig, axs = plt.subplots(1, range_val, figsize=(6*range_val, 6))
    cls_titles = ['cls0', 'cls1', 'cls2']

    #fig1 to combine
    for i in range(range_val):
        vmax_avg = np.percentile(averaged_array[i, range_val:], 90)
        sc = axs.scatter(coords_array[:, 0], coords_array[:, 1], 
                        c=averaged_array[i, range_val:], cmap=cmap, s=3, vmax=vmax_avg, marker='s')
        axs.set_title(cls_titles[i])
        axs.set_xlabel('X Coordinate')
        axs.set_ylabel('Y Coordinate')
        axs.set_aspect('equal')
        fig.colorbar(sc, ax=axs)  # Add colorbar
    
    fig.suptitle('row', fontsize=16)
    plt.tight_layout()
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'attn_heatmap_fig1_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 

    # fig2 to combine
    fig, axs = plt.subplots(1, range_val, figsize=(6*range_val, 6))
    
    for i in range(range_val):
        vmax_avg = np.percentile(averaged_array[range_val:, i], 90)
        sc = axs.scatter(coords_array[:, 0], coords_array[:, 1], 
                        c=averaged_array[range_val:, i], cmap=cmap, s=3, vmax=vmax_avg, marker='s')
        axs.set_title(cls_titles[i])
        axs.set_xlabel('X Coordinate')
        axs.set_ylabel('Y Coordinate')
        axs.set_aspect('equal')
        fig.colorbar(sc, ax=axs)  # Add colorbar
    
    fig.suptitle('col', fontsize=16)
    plt.tight_layout()
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'attn_heatmap_fig2_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 

    # fig3 to combine
    if attn_prob_mtx_cls is not None:
        fig, axs = plt.subplots(1, range_val, figsize=(6*range_val, 6))
        
        for i in range(range_val):
            sc = axs.scatter(coords_array[:, 0], coords_array[:, 1], 
                            c=averaged_array_cls[0, :], cmap=cmap, s=3, vmax=vmax_avg_cls, marker='s')
            axs.set_title(cls_titles[i])
            axs.set_xlabel('X Coordinate')
            axs.set_ylabel('Y Coordinate')
            axs.set_aspect('equal')
            fig.colorbar(sc, ax=axs)  # Add colorbar
        
        fig.suptitle('cls', fontsize=16)
        plt.tight_layout()
        # Save plot with datetime
        save_dir = 'results/attention/plot_results'
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
        os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
        save_path = os.path.join(save_dir, f'attn_heatmap_fig3_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Saved plot to {save_path}")
        plt.close() 


        
def plot_attention_heatmaps_20250210bkup(attn_prob_mtx, 
                            coords, 
                            attn_prob_mtx_cls=None,
                            cmap='rainbow'
                        ):
    # 1. Extract the first attention matrix from the batch of 16
    first_dim_mtx = attn_prob_mtx[0, 0]  # Shape: [769, 769]
    first_dim_array = first_dim_mtx.detach().cpu().numpy() if torch.is_tensor(first_dim_mtx) else first_dim_mtx
    vmax_first = np.percentile(first_dim_array, 95)

    # 2. Average across the 16 attention matrices
    averaged_mtx = attn_prob_mtx.mean(1)[0]  # Shape: [769, 769]
    averaged_mtx_cls = attn_prob_mtx_cls.mean(1)[0]  # Shape: [769, 769]
    
    averaged_array = averaged_mtx.detach().cpu().numpy() if torch.is_tensor(averaged_mtx) else averaged_mtx
    averaged_array_cls = averaged_mtx_cls.detach().cpu().numpy() if torch.is_tensor(averaged_mtx_cls) else averaged_mtx_cls
    vmax_avg = np.percentile(averaged_array, 90)
    # print('attn_prob_mtx_cls',attn_prob_mtx_cls)
    vmax_avg_cls = np.percentile(averaged_array_cls, 90)

    # 3. Plotting coords with attention probabilities
    coords_array = coords.squeeze(0).detach().cpu().numpy() if torch.is_tensor(coords) else coords.squeeze(0)

    range_val=1#3
    fig, axs = plt.subplots(1, range_val, figsize=(6*range_val, 6))
    cls_titles = ['cls0', 'cls1', 'cls2']


    for i in range(range_val):
        vmax_avg = np.percentile(averaged_array[i, range_val:], 90)

        axs.scatter(coords_array[:, 0], coords_array[:, 1], 
                    c=averaged_array[i, range_val:], cmap=cmap, s=30, vmax=vmax_avg,
                    marker='s')
        axs.set_title(cls_titles[i])
        axs.set_xlabel('X Coordinate')
        axs.set_ylabel('Y Coordinate')
        axs.set_aspect('equal')
    fig.suptitle('row', fontsize=16)
    plt.tight_layout()
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'attn_heatmap_coords_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 

    fig, axs = plt.subplots(1, range_val, figsize=(6*range_val, 6))
    cls_titles = ['cls0', 'cls1', 'cls2']


    # fig3 
    for i in range(range_val):
        vmax_avg = np.percentile(averaged_array[range_val:,i], 90)

        axs.scatter(coords_array[:, 0], coords_array[:, 1], c=averaged_array[range_val:,i], cmap=cmap, s=30, vmax=vmax_avg,marker='s')
        axs.set_title(cls_titles[i])
        axs.set_xlabel('X Coordinate')
        axs.set_ylabel('Y Coordinate')
        axs.set_aspect('equal')
    fig.suptitle('col', fontsize=16)

    plt.tight_layout()
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'attn_heatmap_fig3_coord_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 

    if attn_prob_mtx_cls is not None:
        fig, axs = plt.subplots(1, range_val, figsize=(6*range_val, 6))
        cls_titles = ['cls0', 'cls1', 'cls2']
    
        for i in range(range_val):
            axs.scatter(coords_array[:, 0], coords_array[:, 1], c=averaged_array_cls[0,:], cmap=cmap, s=30, vmax=vmax_avg_cls,marker='s')
            axs.set_title(cls_titles[i])
            axs.set_xlabel('X Coordinate')
            axs.set_ylabel('Y Coordinate')
            axs.set_aspect('equal')
        fig.suptitle('col', fontsize=16)
    
        plt.tight_layout()
        # Save plot with datetime
        save_dir = 'results/attention/plot_results'
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
        os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
        save_path = os.path.join(save_dir, f'attn_heatmap_fig3_mtx_coord_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Saved plot to {save_path}")
        plt.close()       

# Example usage:
# Assuming attn_prob_mtx is already defined and has shape [1, 16, 769, 769]
# plot_attention_heatmaps(attn_prob_mtx,coords)
