import torch
import math
import os
from datetime import datetime


def standard_attention(Q: torch.Tensor, 
                       K: torch.Tensor, 
                       V: torch.Tensor) -> torch.Tensor:
    """
    Compute standard multi-head attention given Q, K, V in the shape:
      (batch, seq_len, num_heads, head_dim).
    
    Returns:
      attn_output: (batch, seq_len, num_heads, head_dim)


    # goal - repate flash attn
    # Suppose each dimension is:
    #   batch      = 2
    #   seq_len    = 1024
    #   num_heads  = 16
    #   head_dim   = 48
    Q = torch.randn(2, 1024, 16, 48)
    K = torch.randn(2, 1024, 16, 48)
    V = torch.randn(2, 1024, 16, 48)
    
    attn_output = standard_attention(Q, K, V)
    print("Output shape:", attn_output.shape)
    Should be (2, 1024, 16, 48)

    """
    
    # 1) Extract dimensions
    batch_size, seq_len, num_heads, head_dim = Q.shape
    
    # 2) Transpose to (batch, num_heads, seq_len, head_dim)
    Q_ = Q.transpose(1, 2)  # (batch, num_heads, seq_len, head_dim)
    K_ = K.transpose(1, 2)  # (batch, num_heads, seq_len, head_dim)
    V_ = V.transpose(1, 2)  # (batch, num_heads, seq_len, head_dim)

    # 3) Compute attention scores = Q * K^T
    #    => scores shape: (batch, num_heads, seq_len, seq_len)
    scores = torch.matmul(Q_, K_.transpose(-1, -2))
    
    # 4) Scale by sqrt(head_dim) and apply softmax along last dimension
    scores = scores / math.sqrt(head_dim)
    attn_weights = torch.softmax(scores, dim=-1)  # (b, nH, seq_len, seq_len)

    # 5) Multiply attention weights by V => final context
    #    => attn_output shape: (batch, num_heads, seq_len, head_dim)
    attn_output = torch.matmul(attn_weights, V_)
    
    # 6) Transpose back to (batch, seq_len, num_heads, head_dim)
    attn_output = attn_output.transpose(1, 2)  # => (b, seq_len, nH, head_dim)

    return attn_output,attn_weights

def standard_attention_modified(
    Q: torch.Tensor, 
    K: torch.Tensor, 
    V: torch.Tensor,
    mask: torch.Tensor = None
):
    """
    Multi-head attention with LSE.
    """
    
    # 1) Extract dimensions
    batch_size, seq_len, num_heads, head_dim = Q.shape
    # print('http://localhost:2729/lab/tree/prov-gigapath/gigapath_0/torchscale/component/_utils.py')

    
    # 2) Transpose to (batch, num_heads, seq_len, head_dim)
    Q_ = Q.transpose(1, 2)  # (batch, num_heads, seq_len, head_dim)
    K_ = K.transpose(1, 2)  # (batch, num_heads, seq_len, head_dim)
    V_ = V.transpose(1, 2)  # (batch, num_heads, seq_len, head_dim)

    # 3) Compute raw attention scores: Q * K^T
    scores = torch.matmul(Q_, K_.transpose(-1, -2))
    scores0 = scores.clone()

    # If a mask is provided, mask out unwanted positions
    if mask is not None:
        scores = scores.masked_fill(mask == 0, float('-inf'))

    # 4) Scale by sqrt(head_dim) and apply softmax along last dimension
    scores = scores / math.sqrt(head_dim)
    scores1 = scores.clone()
    
    # 5) Compute LogSumExp for numerical stability
    lse = torch.logsumexp(scores, dim=-1)  # Shape: (batch, num_heads, seq_len)

    # 6) Apply softmax to get attention weights
    attn_weights = torch.softmax(scores, dim=-1)

    # 7) Multiply attention weights by V => final context
    attn_output = torch.matmul(attn_weights, V_)  # (batch, num_heads, seq_len, head_dim)

    # 8) Transpose back to (batch, seq_len, num_heads, head_dim)
    attn_output = attn_output.transpose(1, 2)  # (batch, seq_len, num_heads, head_dim)
    # print('attn_output.shape',attn_output.shape)# attn_output.shape torch.Size([1, 5456, 16, 48])

    return attn_output, attn_weights, scores0, scores1, lse


def standard_attention_first_query(
    Q: torch.Tensor, 
    K: torch.Tensor, 
    V: torch.Tensor,
    mask: torch.Tensor = None
):
    """
    Compute multi-head attention for only the first query token in each sequence.
    
    Args:
        Q (torch.Tensor): Query tensor of shape (batch_size, seq_len, num_heads, head_dim).
        K (torch.Tensor): Key tensor of shape (batch_size, seq_len, num_heads, head_dim).
        V (torch.Tensor): Value tensor of shape (batch_size, seq_len, num_heads, head_dim).
        mask (torch.Tensor, optional): Attention mask of shape 
            (batch_size, num_heads, 1, seq_len). 
            - 1 or True in valid positions
            - 0 or False in positions to mask out
    
    Returns:
        attn_output (torch.Tensor): Attention output for the first query, 
            shape (batch_size, 1, num_heads, head_dim).
        attn_weights (torch.Tensor): Attention weights for the first query, 
            shape (batch_size, num_heads, 1, seq_len).
        scores0 (torch.Tensor): Raw (unscaled) attention scores for the first query, 
            shape (batch_size, num_heads, 1, seq_len).
        scores1 (torch.Tensor): Scaled attention scores before softmax for the first query, 
            shape (batch_size, num_heads, 1, seq_len).
    """
    
    # 1) Extract dimensions
    batch_size, seq_len, num_heads, head_dim = Q.shape
    
    # 2) Transpose to (batch_size, num_heads, seq_len, head_dim)
    Q_ = Q.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)
    K_ = K.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)
    V_ = V.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)

    # 3) Select only the first query token
    # Q_first: (batch_size, num_heads, 1, head_dim)
    Q_first = Q_[:, :, 0:1, :]  # Slicing the first query

    # 4) Compute raw attention scores: Q_first * K_^T
    # K_.transpose(-1, -2): (batch_size, num_heads, head_dim, seq_len)
    # scores: (batch_size, num_heads, 1, seq_len)
    scores = torch.matmul(Q_first, K_.transpose(-1, -2))  # (batch_size, num_heads, 1, seq_len)
    scores0 = scores.clone()  # Raw unscaled scores

    # 5) Apply mask if provided
    if mask is not None:
        # mask shape should be broadcastable to (batch_size, num_heads, 1, seq_len)
        scores = scores.masked_fill(mask == 0, float('-inf'))

    # 6) Scale the scores
    scores = scores / math.sqrt(head_dim)
    scores1 = scores.clone()  # Scaled scores before softmax

    # 7) Apply softmax to get attention weights
    attn_weights = torch.softmax(scores, dim=-1)  # (batch_size, num_heads, 1, seq_len)

    # 8) Multiply attention weights with V_ to get the attention output
    # V_: (batch_size, num_heads, seq_len, head_dim)
    # attn_weights: (batch_size, num_heads, 1, seq_len)
    # attn_output: (batch_size, num_heads, 1, head_dim)
    attn_output = torch.matmul(attn_weights, V_)  # (batch_size, num_heads, 1, head_dim)

    # 9) Transpose back to (batch_size, 1, num_heads, head_dim)
    attn_output = attn_output.transpose(1, 2)  # (batch_size, 1, num_heads, head_dim)

    return attn_output, attn_weights, scores0, scores1

def standard_attention_first_query_modified(
    Q: torch.Tensor, 
    K: torch.Tensor, 
    V: torch.Tensor,
    mask: torch.Tensor = None
):
    """
    Modified multi-head attention for the first query token, returning LSE.
    """
    
    # 1) Extract dimensions
    batch_size, seq_len, num_heads, head_dim = Q.shape
    
    # 2) Transpose to (batch_size, num_heads, seq_len, head_dim)
    Q_ = Q.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)
    K_ = K.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)
    V_ = V.transpose(1, 2)  # (batch_size, num_heads, seq_len, head_dim)

    # 3) Select only the first query token
    Q_first = Q_[:, :, 0:1, :]  # (batch_size, num_heads, 1, head_dim)

    # 4) Compute raw attention scores: Q_first * K_^T
    scores = torch.matmul(Q_first, K_.transpose(-1, -2))  # (batch_size, num_heads, 1, seq_len)
    scores0 = scores.clone()  # Raw unscaled scores

    # 5) Apply mask if provided
    if mask is not None:
        scores = scores.masked_fill(mask == 0, float('-inf'))

    # 6) Scale the scores
    scores = scores / math.sqrt(head_dim)
    scores1 = scores.clone()  # Scaled scores before softmax

    # 7) Compute LogSumExp for numerical stability
    lse = torch.logsumexp(scores, dim=-1)  # Shape: (batch_size, num_heads, 1)

    # 8) Apply softmax to get attention weights
    attn_weights = torch.softmax(scores, dim=-1)  # (batch_size, num_heads, 1, seq_len)

    # 9) Multiply attention weights with V_ to get the attention output
    attn_output = torch.matmul(attn_weights, V_)  # (batch_size, num_heads, 1, head_dim)

    # 10) Transpose back to (batch_size, 1, num_heads, head_dim)
    attn_output = attn_output.transpose(1, 2)  # (batch_size, 1, num_heads, head_dim)

    return attn_output, attn_weights, scores0, scores1, lse


import torch
import matplotlib.pyplot as plt
import seaborn as sns

def visualize_heatmap(tensor: torch.Tensor, cmap: str = "viridis", title: str = "Heatmap"):
    """
    Visualize a 2D torch.Tensor as a heatmap using matplotlib + seaborn.
    
    Args:
        tensor (torch.Tensor): A 2D tensor of shape (rows, cols).
        cmap (str): The colormap to use (e.g. 'viridis', 'hot', 'coolwarm').
        title (str): Title for the heatmap figure.


    # --------------------------
    # Example usage:
    # --------------------------
    if __name__ == "__main__":
        # Create a random 2D tensor
        example_tensor = torch.randn(10, 15)  # shape (10 rows, 15 cols)
    
        # Visualize the tensor as a heatmap
        visualize_heatmap(example_tensor, cmap="coolwarm", title="Random Tensor Heatmap")

    """
    if tensor.dim() != 2:
        raise ValueError("The input 'tensor' must be 2D.")
    
    # Convert tensor to NumPy
    data = tensor.detach().cpu().numpy()
    
    # Create the plot
    plt.figure(figsize=(8, 6))
    sns.heatmap(data, cmap=cmap, annot=False)  # annot=True to show numeric values
    plt.title(title)
    plt.xlabel("Columns")
    plt.ylabel("Rows")
    # Save plot with datetime
    save_dir = 'results/attention/plot_results'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") 
    os.makedirs(save_dir, exist_ok=True)  # Create directory if needed
    save_path = os.path.join(save_dir, f'visualize_heatmap_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {save_path}")
    plt.close() 