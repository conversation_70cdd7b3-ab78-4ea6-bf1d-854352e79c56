name: gigapath
channels:
  - defaults
  - nvidia
  - pytorch
dependencies:
  - pip
  - python=3.9  # Specify the Python version you want to use
  - cuda
  - packaging
  - pytorch==2.0.0
  - torchvision==0.15.0
  - torchaudio==2.0.0
  - pytorch-cuda=11.8
  - pip:
      - omegaconf
      - torchmetrics==0.10.3
      - fvcore
      - iopath
      - xformers==0.0.18
      - huggingface-hub==0.20.2
      - h5py
      - numpy
      - pandas
      - pillow
      - tqdm
      - einops
      - webdataset
      - matplotlib
      - lifelines
      - scikit-survival
      - scikit-learn
      - tensorboard
      - fairscale
      - wandb
      - timm>=1.0.3
      - packaging==23.2
      - ninja==********
      - transformers==4.36.2
      - flash-attn==2.5.8
      - monai
      - openslide-python
      - scikit-image
