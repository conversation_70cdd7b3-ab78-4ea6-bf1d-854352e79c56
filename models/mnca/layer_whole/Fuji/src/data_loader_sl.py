from data.slide_datatset_sl import SlideDataset
import h5py
import logging



def clean_data(args):
    import os
    import glob
    
    path_del=args.split_dir+'/*'
    
    files = glob.glob(path_del)
    for f in files:
        try:
            os.remove(f)
        except:0

print('data_loader_sl.py SlideDataset',SlideDataset)
# DatasetClass = SlideDataset

import pandas as pd
from torch.utils.data import DataLoader, ConcatDataset
from src.utils import seed_torch, get_exp_code, get_splits, get_loader, save_obj
from src.utils import slide_collate_fn
class CustomDatasetWrapper:
    def __init__(self, dataset, type_value='default'):
        self.dataset = dataset
        self.type_value = type_value  # Assign user-defined type

    def __getitem__(self, index):
        item = self.dataset[index]  # Get the original item
        item['type'] = self.type_value  # Add 'type' information
        return item

    def __len__(self):
        return len(self.dataset)

def combine_data_loaders(loaders, types, batch_size=1):
    """
    Combines multiple data loaders into a single data loader with type labels.
    
    Args:
        loaders (list): List of PyTorch DataLoader objects.
        types (list): List of corresponding type labels (strings).
        batch_size (int): Batch size for the final DataLoader.
    
    Returns:
        DataLoader: A new DataLoader containing all datasets with labeled types.
    """
    
    # Ensure loaders and types lists are the same length
    assert len(loaders) == len(types), "Mismatch: loaders and types must have the same length."

    # Wrap datasets with corresponding types
    wrapped_datasets = [CustomDatasetWrapper(loader.dataset, type_value=type_label) 
                        for loader, type_label in zip(loaders, types)]

    # Combine datasets using ConcatDataset
    combined_dataset = ConcatDataset(wrapped_datasets)
    
    # Create a new DataLoader
    combined_loader = DataLoader(combined_dataset, batch_size=batch_size, shuffle=True)
    # # Create a new DataLoader
    # combined_loader = DataLoader(combined_dataset, batch_size=batch_size, shuffle=True, collate_fn=slide_collate_fn)    
    return combined_loader



# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prepare_data_loaders(args, 
                         fold, 
                         test_r=0.01, 
                         selected_samples=None): 
    """
    Prepares train, validation, and test DataLoaders for multiple patch lengths and dataset splits.

    Args:
        args: Argument parser or configuration object with dataset paths, split keys, and configurations.
        fold: The fold number for cross-validation.

    Returns:
        train_loader_lst, val_loader_lst, test_loader_lst: Lists of combined DataLoaders for each patch length.
    """
    train_loader_lst, val_loader_lst, test_loader_lst = [], [], []

    for patch_len in args.patch_len_lst:
        logger.info(f"Processing patch length: {patch_len}")

        train_loader_lst_one_patch_len, val_loader_lst_one_patch_len, test_loader_lst_one_patch_len = [], [], []

        for type_use in args.split_dir_dict.keys():
            args.split_dir = args.split_dir_dict[type_use]
            clean_data(args)
            
            dataset = pd.read_csv(args.dataset_csv[type_use])  # Read the dataset CSV file
            dataset['label']=dataset['label_add']

            dataset['slide_id_main'] = dataset['slide_id'].str.replace(r'_aug_\d+', '', regex=True)
            
            dataset_for_filter=dataset.copy()
            dataset_for_filter['slide_id'] = dataset_for_filter['slide_id'].str.replace(r'_aug_\d+', '', regex=True)
            dataset_for_filter=dataset_for_filter.drop_duplicates().copy()

            # not working in terminal as py
            print(dataset_for_filter)
            # Get train/validation/test splits
            train_splits, val_splits, test_splits = get_splits(dataset_for_filter, 
                                                            fold=fold, 
                                                            test_r=test_r, 
                                                            **vars(args))
            

            dataset = dataset.drop_duplicates(subset='slide_id_main')

            train_splits=list(dataset[dataset.slide_id_main.isin(train_splits)].slide_id)
            val_splits=list(dataset[dataset.slide_id_main.isin(val_splits)].slide_id)
            test_splits=list(dataset[dataset.slide_id_main.isin(test_splits)].slide_id)

            if selected_samples is not None:
                train_splits, val_splits, test_splits = selected_samples, selected_samples, selected_samples
            

            train_data = SlideDataset(dataset, args.root_path[type_use], train_splits, args.task_config, 
                                    split_key=args.split_key, patch_len=patch_len, tile_emb_path=args.tile_emb_path)
            
            val_data = SlideDataset(dataset, args.root_path[type_use], val_splits, args.task_config, 
                                    split_key=args.split_key, patch_len=patch_len, tile_emb_path=args.tile_emb_path) if len(val_splits) > 0 else None
            test_data = SlideDataset(dataset, args.root_path[type_use], test_splits, args.task_config, 
                                    split_key=args.split_key, patch_len=patch_len, tile_emb_path=args.tile_emb_path) if len(test_splits) > 0 else None

            # Get DataLoaders
            train_loader, val_loader, test_loader = get_loader(train_data, val_data, test_data, **vars(args))

            logger.info(f"  {type_use} - Train size: {len(train_loader.dataset)} | "
                        f"Val size: {len(val_loader.dataset) if val_loader else 0} | "
                        f"Test size: {len(test_loader.dataset) if test_loader else 0}")

            train_loader_lst_one_patch_len.append(train_loader)
            val_loader_lst_one_patch_len.append(val_loader)
            test_loader_lst_one_patch_len.append(test_loader)

        # Combine DataLoaders for this patch length
        train_combined_loader = combine_data_loaders(
            loaders=train_loader_lst_one_patch_len,
            types=list(args.split_dir_dict.keys()),  # Convert dict_keys to list
            batch_size=args.batch_size
        )
        val_combined_loader = combine_data_loaders(
            loaders=val_loader_lst_one_patch_len,
            types=list(args.split_dir_dict.keys()),
            batch_size=args.batch_size
        )
        test_combined_loader = combine_data_loaders(
            loaders=test_loader_lst_one_patch_len,
            types=list(args.split_dir_dict.keys()),
            batch_size=args.batch_size
        )
    
        train_loader_lst.append(train_combined_loader)
        val_loader_lst.append(val_combined_loader)
        test_loader_lst.append(test_combined_loader)

        # Log combined loader sizes
        logger.info(f"  Combined Train Loader Size: {len(train_combined_loader.dataset)}")
        logger.info(f"  Combined Val Loader Size: {len(val_combined_loader.dataset) if val_combined_loader else 0}")
        logger.info(f"  Combined Test Loader Size: {len(test_combined_loader.dataset) if test_combined_loader else 0}")

    # Log final loader sizes
    logger.info("\nFinal Loader Sizes in Lists:")
    for i, (train_loader, val_loader, test_loader) in enumerate(zip(train_loader_lst, val_loader_lst, test_loader_lst)):
        logger.info(f"  Patch index {i} - Train: {len(train_loader.dataset)} | "
                    f"Val: {len(val_loader.dataset) if val_loader else 0} | "
                    f"Test: {len(test_loader.dataset) if test_loader else 0}")

    return train_loader_lst, val_loader_lst, test_loader_lst

