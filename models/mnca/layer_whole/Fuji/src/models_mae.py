# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.

# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.
# --------------------------------------------------------
# References:
# timm: https://github.com/rwightman/pytorch-image-models/tree/master/timm
# DeiT: https://github.com/facebookresearch/deit
# --------------------------------------------------------

from functools import partial

import torch
import torch.nn as nn

from timm.models.vision_transformer import PatchEmbed, Block

from src.pos_embed import get_2d_sincos_pos_embed, get_2d_sincos_pos_embed_from_grid


class MaskedAutoencoderViT(nn.Module):
    """ Masked Autoencoder with VisionTransformer backbone
    """
    def __init__(self, img_size=224, patch_size=16, in_chans=3,
                 embed_dim=1024, depth=24, num_heads=16,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 mlp_ratio=4., norm_layer=nn.LayerNorm, norm_pix_loss=False):
        super().__init__()
        self._scaler = torch.cuda.amp.GradScaler()
        self.slide_ngrids=1000
        # MAE encoder specifics
        print('img_size:', img_size, ' patch_size:', patch_size, ' in_chans:', in_chans, ' embed_dim:', embed_dim)
        self.patch_embed = PatchEmbed(img_size, patch_size, in_chans, embed_dim)
        num_patches = self.patch_embed.num_patches

        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        # self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim), requires_grad=False)  # fixed sin-cos embedding
        self.pos_embed = nn.Parameter(torch.zeros(1,1000000 + 1, embed_dim), requires_grad=False)  # fixed sin-cos embedding

        self.blocks = nn.ModuleList([
            Block(embed_dim, num_heads, mlp_ratio, qkv_bias=True, 
                  # qk_scale=None, 
                  norm_layer=norm_layer)
            for i in range(depth)])
        self.norm = norm_layer(embed_dim)
        # --------------------------------------------------------------------------

        # --------------------------------------------------------------------------
        # MAE decoder specifics
        self.decoder_embed = nn.Linear(embed_dim, decoder_embed_dim, bias=True)

        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))

        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, decoder_embed_dim), requires_grad=False)  # fixed sin-cos embedding

        self.decoder_blocks = nn.ModuleList([
            Block(decoder_embed_dim, decoder_num_heads, mlp_ratio, qkv_bias=True, 
                  # qk_scale=None, 
                  norm_layer=norm_layer)
            for i in range(decoder_depth)])

        self.decoder_norm = norm_layer(decoder_embed_dim)
        self.decoder_pred = nn.Linear(decoder_embed_dim, 
                                      1536,
                                      # patch_size**2 * in_chans, 
                                      bias=True) # decoder to patch
        # --------------------------------------------------------------------------

        self.norm_pix_loss = norm_pix_loss

        self.initialize_weights()



        
    def initialize_weights(self):
        # initialization
        # initialize (and freeze) pos_embed by sin-cos embedding
        # pos_embed = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], int(self.patch_embed.num_patches**.5), cls_token=True)
        # self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))


        
        pos_embed = get_2d_sincos_pos_embed(768, 1000, cls_token=True) #768
        # print('pos_embed.shape',pos_embed.shape)
        # print(' self.pos_embed.shape', self.pos_embed.shape)
        # self.pos_embed=None
        # print(self.pos_embed.data.shape)
        # print(pos_embed.shape)
        self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))
        num_patches=1000000
        # print('self.decoder_pos_embed.shape',self.decoder_pos_embed.shape)
        # print('self.patch_embed.num_patches', self.patch_embed.num_patches)
        # self.decoder_pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, decoder_embed_dim), requires_grad=False)
                
        # decoder_pos_embed = get_2d_sincos_pos_embed(768, self.slide_ngrids, cls_token=True)
        # self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))

        # initialize patch_embed like nn.Linear (instead of nn.Conv2d)
        w = self.patch_embed.proj.weight.data
        torch.nn.init.xavier_uniform_(w.view([w.shape[0], -1]))

        # timm's trunc_normal_(std=.02) is effectively normal_(std=0.02) as cutoff is too big (2.)
        torch.nn.init.normal_(self.cls_token, std=.02)
        torch.nn.init.normal_(self.mask_token, std=.02)

        # initialize nn.Linear and nn.LayerNorm
        self.apply(self._init_weights)
    
    def initialize_weights_pos_embed(self,len_customize):
        decoder_pos_embed = get_2d_sincos_pos_embed_from_grid(self.embed_dim, int(self.patch_embed.num_patches**.5), cls_token=True)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))
    
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            # we use xavier_uniform following official JAX ViT:
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def patchify(self, imgs):
        """
        imgs: (N, 3, H, W)
        x: (N, L, patch_size**2 *3)
        """
        p = self.patch_embed.patch_size[0]
        # print('imgs.shape',imgs.shape)
        # print('p',p)
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0

        h = w = imgs.shape[2] // p
        x = imgs.reshape(shape=(imgs.shape[0], 3, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * 3))
        return x

    def unpatchify(self, x):
        """
        x: (N, L, patch_size**2 *3)
        imgs: (N, 3, H, W)
        """
        # print('unpatchify', x.shape)
        p = self.patch_embed.patch_size[0]
        h = w = int(x.shape[1]**.5)
        assert h * w == x.shape[1]
        
        # print('unpatchify  before reshape', x.shape) # torch.Size([1, 400, 768])
        x = x.reshape(shape=(x.shape[0], h, w, p, p, 3))
        # print('unpatchify  after reshape', x.shape) # torch.Size([1, 20, 20, 16, 16, 3])
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], 3, h * p, h * p))
        return imgs

    def random_masking(self, x, mask_ratio):
        """
        Perform per-sample random masking by per-sample shuffling.
        Per-sample shuffling is done by argsort random noise.
        x: [N, L, D], sequence
        """
        N, L, D = x.shape  # batch, length, dim
        len_keep = int(L * (1 - mask_ratio))
        
        noise = torch.rand(N, L, device=x.device)  # noise in [0, 1]
        
        # sort noise for each sample
        ids_shuffle = torch.argsort(noise, dim=1)  # ascend: small is keep, large is remove
        ids_restore = torch.argsort(ids_shuffle, dim=1)

        # keep the first subset
        ids_keep = ids_shuffle[:, :len_keep]
        x_masked = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))

        # generate the binary mask: 0 is keep, 1 is remove
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        # unshuffle to get the binary mask
        mask = torch.gather(mask, dim=1, index=ids_restore)

        return x_masked, mask, ids_restore

    def forward_encoder(self, x, mask_ratio):
        # embed patches
        # print('models_mae.py before patch_embed x.shape',x.shape)
        x = self.patch_embed(x)
        # print('models_mae.py after patch_embed x.shape',x.shape)
        # print('models_mae.py self.pos_embed.shape',self.pos_embed.shape)
        # add pos embed w/o cls token
        x = x + self.pos_embed[:, 1:, :]
        # print('models_mae.py after add pos_embed x.shape',x.shape)
        
        # masking: length -> length * mask_ratio
        x, mask, ids_restore = self.random_masking(x, mask_ratio)

        # append cls token
        cls_token = self.cls_token + self.pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)
        
        # apply Transformer blocks
        for blk in self.blocks:
            x = blk(x)
            
        x = self.norm(x) # 1,101,1024
        # print('models_mae.py forward_encoder last x.shape',x.shape)

        return x, mask, ids_restore

    def forward_decoder(self, x, ids_restore):
        # embed tokens
        x = self.decoder_embed(x)

        # print('forward_decoder decoder_embed x', x.shape) #torch.Size([1, 101, 512])
        # append mask tokens to sequence
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        
        # print('forward_decoder mask_token x', x.shape) #torch.Size([1, 101, 512])
        x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # no cls token
        
        # print('forward_decoder cat x', x.shape) # torch.Size([1, 101, 512])
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))  # unshuffle
        
        # print('forward_decoder gather x', x.shape)#x torch.Size([1, 101, 512]
        x = torch.cat([x[:, :1, :], x_], dim=1)  # append cls token

        # print('forward_decoder cat x', x.shape) # torch.Size([1, 401, 512])
        # add pos embed
        # print('self.decoder_pos_embed', self.decoder_pos_embed.shape) # torch.Size([1, 401, 512])
        x = x + self.decoder_pos_embed

        # print('forward_decoder decoder_pos_embed x', x.shape) #torch.Size([1, 401, 512])

        # apply Transformer blocks
        # print('238 x.dtype', x.dtype)
        for blk in self.decoder_blocks:
            # print('forward_decoder blk',blk)
            # print('forward_decoder blk',blk)
            # for name, param in blk.named_parameters():
            #     print(f"Layer: {name}, Precision: {param.dtype}")
            # print('244 x.dtype', x.dtype)
            x = blk(x)
        x = self.decoder_norm(x)

        # predictor projection
        x = self.decoder_pred(x)

        # remove cls token
        x = x[:, 1:, :]

        return x
        
    def coords_to_pos(self, coords, tile_size: int = 256):
        """
        This function is used to convert the coordinates to the positional indices

        Arguments:
        ----------
        coords: torch.Tensor
            The coordinates of the patches, of shape [N, L, 2]
        output: torch.Tensor
            The positional indices of the patches, of shape [N, L]
        """
        coords_ = torch.floor(coords / tile_size)
        pos = coords_[..., 0] * self.slide_ngrids + coords_[..., 1]
        return pos.long() + 1  # add 1 for the cls token

        
    # def forward_decoder_customize(self, x, coords, ids_restore):
    #     # embed tokens
    #     x = self.decoder_embed(x)
    
    #     print('forward_decoder coords x', coords.shape)  # torch.Size([1, 400, 2])
    #     print('forward_decoder decoder_embed x', x.shape)  # torch.Size([1, 101, 512])
    #     print(f"x requires_grad: {x.requires_grad}")
    
    #     # append mask tokens to sequence
    #     mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        
    #     print('forward_decoder mask_token x', x.shape)  # torch.Size([1, 101, 512])
    #     print(f"mask_tokens requires_grad: {mask_tokens.requires_grad}")
    
    #     x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # no cls token
    
    #     print('forward_decoder cat x', x.shape)  # torch.Size([1, 101, 512])
    #     print(f"x_ requires_grad: {x_.requires_grad}")
    
    #     x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))  # unshuffle
        
    #     print('forward_decoder gather x', x.shape)  # torch.Size([1, 101, 512])
    #     print(f"x_ (after gather) requires_grad: {x_.requires_grad}")
    
    #     x = torch.cat([x[:, :1, :], x_], dim=1)  # append cls token
    
    #     print('forward_decoder cat x', x.shape)  # torch.Size([1, 401, 512])
    #     print(f"x requires_grad: {x.requires_grad}")
    
    #     # add pos embed
    #     print('self.decoder_pos_embed', self.decoder_pos_embed.shape)  # torch.Size([1, 401, 512])
    
    #     pos = self.coords_to_pos(coords, 256)
    
    #     print(f"pos requires_grad: {pos.requires_grad}")  # Expected to be False (indices do not require grad)
    
    #     # Create the zero tensor you want to prepend, of shape [1, 1, 768]
    #     zeros_to_prepend = torch.zeros(1, 1, 768, device=x.device, requires_grad=True)  # 768
    
    #     print(f"zeros_to_prepend requires_grad: {zeros_to_prepend.requires_grad}")
    
    #     # Concatenate along the second dimension (dim=1) to get shape [1, 401, 768]
    #     x_with_zeros = torch.cat([zeros_to_prepend, self.pos_embed[:, pos, :].squeeze(0)], dim=1)
    
    #     print('forward_decoder xxx x', x.shape)  # torch.Size([1, 401, 512])
    #     print(self.pos_embed[:, pos, :].squeeze(0).shape)  # torch.Size([1, 400, 768])
    #     print(pos.shape)  # torch.Size([1, 400])
    
    #     print('x_with_zeros.shape', x_with_zeros.shape)
    #     print(f"x_with_zeros requires_grad: {x_with_zeros.requires_grad}")
    #     print('x.shape', x.shape)
    
    #     x = x + x_with_zeros
    
    #     print('forward_decoder decoder_pos_embed x', x.shape)  # torch.Size([1, 401, 512])
    #     print(f"x (after pos embed) requires_grad: {x.requires_grad}")
    
    #     # apply Transformer blocks
    #     for blk in self.decoder_blocks:
    #         x = blk(x)
    #         break
    #         print(f"x (after blk) requires_grad: {x.requires_grad}")
    
    #     # x = self.decoder_norm(x)
    #     # print(f"x (after decoder_norm) requires_grad: {x.requires_grad}")
    
    #     # # predictor projection
    #     # x = self.decoder_pred(x)
    #     # print(f"x (after decoder_pred) requires_grad: {x.requires_grad}")
    
    #     # remove cls token
    #     x = x[:, 1:, :]
    #     print(f"x (final output) requires_grad: {x.requires_grad}")
    
    #     return x

    def forward_decoder_customize(self, x, coords, ids_restore, args_fp16=True):


        # print('=== http://localhost:3339/lab/workspaces/auto-b/tree/mae-main/models_mae.py ===')
        # print('x.shape', x.shape)
        # print('coords.shape', coords.shape)
        # print('ids_restore.shape', ids_restore.shape)
        
        # with torch.cuda.amp.autocast(dtype=torch.float16 if args_fp16 else torch.float32):
        if True:
            # embed tokens
            x = self.decoder_embed(x)
    
            # print('forward_decoder coords x', coords.shape) # torch.Size([1, 400, 2])
            # print('forward_decoder decoder_embed x', x.shape) #torch.Size([1, 101, 512])
            # append mask tokens to sequence
            mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
            
            # print('forward_decoder mask_token x', x.shape) #torch.Size([1, 101, 512])
            x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # no cls token
            
            # print('forward_decoder cat x', x.shape) # torch.Size([1, 101, 512])
            x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))  # unshuffle
            
            # print('forward_decoder gather x', x.shape)#x torch.Size([1, 101, 512]
            x = torch.cat([x[:, :1, :], x_], dim=1)  # append cls token
    
            # print('forward_decoder cat x', x.shape) # torch.Size([1, 401, 512])
            # add pos embed
            # print('self.decoder_pos_embed', self.decoder_pos_embed.shape) # torch.Size([1, 401, 512])
            
            pos = self.coords_to_pos(coords, 256)
            
            # Create the zero tensor you want to prepend, of shape [1, 1, 768]
            zeros_to_prepend = torch.zeros(1, 1, 768, device=x.device, requires_grad=True) #768
            # Concatenate along the second dimension (dim=1) to get shape [1, 401, 768]
            x_with_zeros = torch.cat([zeros_to_prepend, self.pos_embed[:, pos, :].squeeze(0)], dim=1)
            
            # print('forward_decoder xxx x', x.shape) #torch.Size([1, 401, 512])
            # print(self.pos_embed[:, pos, :].squeeze(0).shape) # torch.Size([1, 400, 768])
            # print(pos.shape) # torch.Size([1, 400])
    
            # print('x_with_zeros.shape', x_with_zeros.shape)
            # print('x.shape', x.shape)
            
            x = x + x_with_zeros
    
            # print('forward_decoder decoder_pos_embed x', x.shape) #torch.Size([1, 401, 512])
    
            # apply Transformer blocks
            # print('391 x.dtype', x.dtype)
            for blk in self.decoder_blocks:
                # print('forward_decoder blk',blk)
                # x = torch.nan_to_num(x, nan=0.0, posinf=0.0, neginf=0.0)
                
                # print('forward_decoder blk',blk)
                # print('forward_decoder blk',blk)
                # for name, param in blk.named_parameters():
                #     print(f"Layer: {name}, Precision: {param.dtype}")
                # print('400 x.dtype', x.dtype)
                
                x = blk(x)
                # x.flatten().sum().backward()

                
                # Compute the scaled sum
                # scaled_output = self._scaler.scale(x.flatten().sum())
                # Compute the gradient of scaled_output with respect to x
                # grad_x = torch.autograd.grad(scaled_output, x, create_graph=True)[0]
                

            x = self.decoder_norm(x)
    
            # predictor projection
            x = self.decoder_pred(x)
    
            # remove cls token
            x = x[:, 1:, :]

        return x
        
    def forward_loss(self, imgs, pred, mask):
        """
        imgs: [N, 3, H, W]
        pred: [N, L, p*p*3]
        mask: [N, L], 0 is keep, 1 is remove, 
        """
        target = self.patchify(imgs)
        # print('target.shape',target.shape) # torch.Size([1, 400, 768])
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5
        # print('models_mae.py target.shape', target.shape) # torch.Size([1, 400, 768])
        loss = (pred - target) ** 2
        # print('models_mae.py pred.shape', pred.shape) # torch.Size([1, 400, 768])
        loss = loss.mean(dim=-1)  # [N, L], mean loss per patch

        loss = (loss * mask).sum() / mask.sum()  # mean loss on removed patches
        return loss
        
    def extract_msk_ids_restore(self, embedding, mask_ratio=0.75):
        x, mask, ids_restore = self.random_masking(embedding, mask_ratio)
        return(x[:,:,0:-2], x[:,:,-2:], mask, ids_restore)
        
    def loss_customize(self, pred, target, mask):
        # pred: torch.Size([1, N, 768]) N=tile amount
        # target: torch.Size([1, N, 768])
        # print('pred.shape',pred.shape)
        # print('target.shape',target.shape)
        # print("Checking pred for NaN/Inf:", torch.isnan(pred).any().item(), torch.isinf(pred).any().item())
        # print("Checking target for NaN/Inf:", torch.isnan(target).any().item(), torch.isinf(target).any().item())
        # print("Checking mask for NaN/Inf:", torch.isnan(mask).any().item(), torch.isinf(mask).any().item())
        pred = pred.to(torch.float32)
        target = target.to(torch.float32)
        mask = mask.to(torch.float32)
        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)
        loss = (loss * mask).sum() / mask.sum()
        return loss

    def forward(self, imgs, mask_ratio=0.75):

        latent, mask, ids_restore = self.forward_encoder(imgs, mask_ratio)

        pred = self.forward_decoder(latent, ids_restore)  # [N, L, p*p*3]
        loss = self.forward_loss(imgs, pred, mask)
        return loss, pred, mask


def mae_vit_base_patch16_dec512d8b(**kwargs):
    model = MaskedAutoencoderViT(
        patch_size=16, embed_dim=768, depth=12, num_heads=12,
        decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
        mlp_ratio=4, norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


def mae_vit_large_patch16_dec512d8b(**kwargs):
    model = MaskedAutoencoderViT(
        patch_size=16, embed_dim=1024, depth=24, num_heads=16,
        decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
        mlp_ratio=4, norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


def mae_vit_huge_patch14_dec512d8b(**kwargs):
    model = MaskedAutoencoderViT(
        patch_size=14, embed_dim=1280, depth=32, num_heads=16,
        decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
        mlp_ratio=4, norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


# set recommended archs
mae_vit_base_patch16 = mae_vit_base_patch16_dec512d8b  # decoder: 512 dim, 8 blocks
mae_vit_large_patch16 = mae_vit_large_patch16_dec512d8b  # decoder: 512 dim, 8 blocks
mae_vit_huge_patch14 = mae_vit_huge_patch14_dec512d8b  # decoder: 512 dim, 8 blocks
