{"task_cfg_path": "./src/task_configs/2-0-Train-15Sig.yaml", "exp_name": "Sig20_replace_giga_raw_after_distill", "pat_strat": false, "dataset_csv": {"APOBEC": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/APOBEC_datasetCSV.txt", "SBS1": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS1_datasetCSV.txt", "SBS15": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS15_datasetCSV.txt", "SBS17": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS17_datasetCSV.txt", "SBS18": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS18_datasetCSV.txt", "SBS20": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS20_datasetCSV.txt", "SBS29": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS29_datasetCSV.txt", "SBS30": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS30_datasetCSV.txt", "SBS3": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS3_datasetCSV.txt", "SBS4": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS4_datasetCSV.txt", "SBS40": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS40_datasetCSV.txt", "SBS10": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS10_datasetCSV.txt", "SBS44": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS44_datasetCSV.txt", "SBS5": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS5_datasetCSV.txt", "SBS7": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/z-2-1-Split-datasetCSV-Train/SBS7_datasetCSV.txt"}, "split_dir": "results/attention_all/foo", "pre_split_dir": "results/attention_all/foo", "root_path": {"APOBEC": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/APOBEC/", "SBS1": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS1/", "SBS15": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS15/", "SBS17": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS17/", "SBS18": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS18/", "SBS20": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS20/", "SBS29": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS29/", "SBS30": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS30/", "SBS3": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS3/", "SBS4": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS4/", "SBS40": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS40/", "SBS10": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS10/", "SBS44": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS44/", "SBS5": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS5/", "SBS7": "/home/<USER>/wanglab/shared/gigapath-l0/1-h5/TCGA-DX/WES-31Cancer-14Sig/SBS7/"}, "tile_size": 256, "max_wsi_size": 250000, "model_arch": "gigapath_slide_enc12l768d", "input_dim": 1536, "latent_dim": 768, "feat_layer": "12", "pretrained": "/pretrained/slide_encoder.pth", "freeze": false, "global_pool": false, "seed": 42, "epochs": 16, "warmup_epochs": 1, "batch_size": 1, "lr": 1.25e-05, "blr": 0.0001, "min_lr": 1e-06, "lr_scheduler": "cosine", "gc": 32, "folds": 1, "optim": "adamw", "optim_wd": 0.05, "layer_decay": 0.95, "dropout": 0.1, "drop_path_rate": 0.0, "val_r": 0.19, "model_select": "last_epoch", "save_dir": "results/attention_all/Cancer32_loss23_Indep_1Aug-15Sig/Sig20_replace_giga_raw_after_distill/eval_pretrained_Cancer32_loss23_Indep_1Aug-15Sig", "num_workers": 30, "report_to": "tensorboard", "fp16": true, "weighted_sample": false, "train_mode": "train", "tile_emb_path": "/home/<USER>/wanglab/shared/Jian/Pro-1-HE-WSI/1-to-SY/ContrastiveH5__TCGA-DX/", "save_latent_emb_path": "", "n_classes": 2, "batch_show_node": 3, "batch_save_node": 50000, "force_lr_after_e0": null, "patch_len_lst": [5000], "plt_attn_compare": false, "train_from_checkpoint_giga_and_customize12Layer": false, "save_emb": "", "w1": 0, "w2": 1, "w3": 1, "w4": 1, "split_dir_dict": {"APOBEC": "./data/z-sj-try-Split/APOBEC/", "SBS1": "./data/z-sj-try-Split/SBS1/", "SBS15": "./data/z-sj-try-Split/SBS15/", "SBS17": "./data/z-sj-try-Split/SBS17/", "SBS18": "./data/z-sj-try-Split/SBS18/", "SBS20": "./data/z-sj-try-Split/SBS20/", "SBS29": "./data/z-sj-try-Split/SBS29/", "SBS30": "./data/z-sj-try-Split/SBS30/", "SBS3": "./data/z-sj-try-Split/SBS3/", "SBS4": "./data/z-sj-try-Split/SBS4/", "SBS40": "./data/z-sj-try-Split/SBS40/", "SBS10": "./data/z-sj-try-Split/SBS10/", "SBS44": "./data/z-sj-try-Split/SBS44/", "SBS5": "./data/z-sj-try-Split/SBS5/", "SBS7": "./data/z-sj-try-Split/SBS7/"}, "sig_type_head_dict": {"APOBEC": 0, "SBS1": 1, "SBS15": 2, "SBS17": 3, "SBS18": 4, "SBS20": 5, "SBS29": 6, "SBS30": 7, "SBS3": 8, "SBS10": 9, "SBS4": 10, "SBS44": 11, "SBS40": 12, "SBS5": 13, "SBS7": 14}, "task_config": {"name": "Cancer32_loss23_Indep_1Aug-15Sig", "setting": "multi_class", "label_dict": {"0": 0, "1": 1}, "max_tiles": 1000000, "shuffle_tiles": true, "add_metrics": ["qwk"]}, "task": "Cancer32_loss23_Indep_1Aug-15Sig", "model_code": "eval_pretrained", "task_code": "Cancer32_loss23_Indep_1Aug-15Sig", "exp_code": "eval_pretrained_Cancer32_loss23_Indep_1Aug-15Sig", "split_key": "slide_id"}