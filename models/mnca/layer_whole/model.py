import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal, Distribution
import numpy as np

def depthwise_conv(x, filters, padding='circular'):
    """filters: [filter_n, h, w]"""
    b, ch, h, w = x.shape
    y = x.reshape(b * ch, 1, h, w)
    y = torch.nn.functional.pad(y, [1, 1, 1, 1], padding)
    y = torch.nn.functional.conv2d(y, filters[:, None])
    return y.reshape(b, -1, h, w)


def merge_lap(z):
    # This function merges the lap_x and lap_y into a single laplacian filter
    b, c, h, w = z.shape
    z = torch.stack([
        z[:, ::5],
        z[:, 1::5],
        z[:, 2::5],
        z[:, 3::5] + z[:, 4::5]
    ],
        dim=2)  # [b, chn, 4, h, w]
    return z.reshape(b, -1, h, w)  # [b, 4 * chn, h, w]


class CPE2D(nn.Module):
    """
    Cartesian Positional Encoding 2D
    """

    def __init__(self):
        super(CPE2D, self).__init__()
        self.cached_penc = None
        self.last_tensor_shape = None

    def forward(self, tensor):
        """
        :param tensor: A tensor of size (batch_size, ch, x, y)
        :return: Positional Encoding Matrix of size (batch_size, 2, x, y)
        """
        if len(tensor.shape) != 4:
            if isinstance(tensor, tuple) and len(tensor) == 2:
                tensor = tensor[0]

            if len(tensor.shape) == 3:
                tensor = tensor.unsqueeze(0)  # 添加批量维度
            elif len(tensor.shape) == 2:
                tensor = tensor.unsqueeze(0).unsqueeze(0)  # 添加批量和通道维度

            if len(tensor.shape) != 4:
                raise RuntimeError(f"Cannot convert tensor of shape {tensor.shape} to 4D!")

        if self.cached_penc is not None and self.last_tensor_shape == tensor.shape:
            return self.cached_penc

        self.cached_penc = None
        batch_size, orig_ch, h, w = tensor.shape
        xs = torch.arange(h, device=tensor.device) / h # [0,1,2,3,4]/5 -> [0,1)
        ys = torch.arange(w, device=tensor.device) / w
        xs = 2.0 * (xs - 0.5 + 0.5 / h) # -> [-1, 1]
        ys = 2.0 * (ys - 0.5 + 0.5 / w)
        xs = xs[None, :, None]
        ys = ys[None, None, :]
        emb = torch.zeros((2, h, w), device=tensor.device).type(tensor.type())
        emb[:1] = xs
        emb[1: 2] = ys

        self.cached_penc = emb.unsqueeze(0).repeat(batch_size, 1, 1, 1) # -> [b, 2, h, w]
        self.last_tensor_shape = tensor.shape

        return self.cached_penc


class SimpleUpdateNet(nn.Module):
    """Simple update network for VNCA with N convolutional layers"""
    def __init__(self, n_channels, hidden_dim=128, output_channels=None, n_layers=3):
        super().__init__()
        if output_channels is None:
            output_channels = n_channels
        if n_layers < 2:
            raise ValueError("Number of layers must be at least 2")
            
        self.n_layers = n_layers
        
        # self.conv_first = nn.Conv2d(n_channels, hidden_dim, kernel_size=1 )
        self.conv_first = nn.Conv2d(n_channels, hidden_dim, kernel_size=3, padding=1)
        torch.nn.init.xavier_normal_(self.conv_first.weight)
        
        self.middle_convs = nn.ModuleList()
        for _ in range(n_layers - 2):
            conv = nn.Conv2d(hidden_dim, hidden_dim, kernel_size=1)
            torch.nn.init.xavier_normal_(conv.weight)
            self.middle_convs.append(conv)
        
        self.conv_last = nn.Conv2d(hidden_dim, output_channels, kernel_size=1)
        torch.nn.init.zeros_(self.conv_last.weight)
        
        self.dropout = nn.Dropout2d(p=0.2)

    def forward(self, x):
        h = self.dropout(torch.relu(self.conv_first(x)))
        
        for conv in self.middle_convs:
            h = self.dropout(torch.relu(conv(h)))
            
        return self.conv_last(h)


class DyNCA(torch.nn.Module):
    """
    Parameters
    ----------
    c_in: int, required
        Number of channels in the input
        Note that each channel will be processed
        using 3, or 4 (if laplacian=True) convolution filters
    c_out: int, required
        Number of channels in the output
        Note that the NCA will be performed using c_in channels
        and the output of the NCA will be expanded to c_out
        channels using a learnable 1x1 convolution layer
    fc_dim: int, default=94
        Number of channels in the intermediate fully connected layer
    random_seed: int, default=None
    seed_mode: {'zeros', 'center_on', 'random'}, default='constant'
        Type of the seed used to initialize the cellular automata
    device: pytorch device
        Device used for performing the computation.
    """

    def __init__(self, c_in, c_out, n_layers=3, embed_dim=20, fc_dim=96,
                 padding_mode='replicate',
                 pos_emb='CPE',
                 perception_scales=[0],
                 device=None):

        super().__init__()
        self.c_in = 50
        self.c_out = c_out
        self.perception_scales = perception_scales
        self.fc_dim = fc_dim
        self.padding_mode = padding_mode
        self.random_seed = 42
        self.pos_emb = pos_emb
        self.device = device
        self.expand = 4

        self.c_cond = 0
        if self.pos_emb == 'CPE':
            self.pos_emb_2d = CPE2D()
            self.c_cond += 2
        else:
            self.pos_emb_2d = None

        self.w1 = torch.nn.Conv2d(self.c_in * self.expand + self.c_cond, self.fc_dim, 1, device=self.device)
        torch.nn.init.xavier_normal_(self.w1.weight, gain=0.2)

        self.w2 = torch.nn.Conv2d(self.fc_dim, self.c_in, 1, bias=True, device=self.device)
        torch.nn.init.xavier_normal_(self.w2.weight, gain=0.1)
        torch.nn.init.zeros_(self.w2.bias)

        self.update_net = SimpleUpdateNet(self.c_in * self.expand + self.c_cond, self.fc_dim, output_channels=self.c_in, n_layers=n_layers)

        self.sobel_filter_x = torch.FloatTensor([[-1.0, 0.0, 1.0], [-2.0, 0.0, 2.0], [-1.0, 0.0, 1.0]]).to(
            self.device)
        self.sobel_filter_y = self.sobel_filter_x.T

        self.identity_filter = torch.FloatTensor([[0, 0, 0], [0, 1, 0], [0, 0, 0]]).to(self.device)
        self.laplacian_filter = torch.FloatTensor([[1.0, 2.0, 1.0], [2.0, -12, 2.0], [1.0, 2.0, 1.0]]).to(self.device)

        # self.learnable_filter_3x3 = nn.Parameter(torch.randn(3, 3)).to(self.device)
        # self.learnable_filter_5x5 = nn.Parameter(torch.randn(5, 5)).to(self.device)
        # self.learnable_filter_7x7 = nn.Parameter(torch.randn(7, 7)).to(self.device)

        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(self.c_in * self.expand, self.c_in * self.expand // 4, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(self.c_in * self.expand // 4, self.c_in * self.expand, kernel_size=1),
            nn.Sigmoid()
        )
        
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(self.c_in * self.expand * 2, self.c_in * self.expand, kernel_size=7, padding=3),
            nn.ReLU(),
            nn.Conv2d(self.c_in * self.expand, self.c_in * self.expand, kernel_size=1),
            nn.Sigmoid()
        )

        self.p_z = Normal(torch.zeros(self.c_in, device=self.device),
                          torch.ones(self.c_in, device=self.device))
                        


    def perceive_torch(self, x, scale=0):
        assert scale in [0, 1, 2, 3, 4, 5] 
        if scale != 0:
            _, _, h, w = x.shape
            h_new = int(h // (2 ** scale))
            w_new = int(w // (2 ** scale))
            x = F.interpolate(x, size=(h_new, w_new), mode='bilinear', align_corners=False) # 1/(2^scale)

        def _perceive_with_torch(z, weight):
            conv_weights = weight.reshape(1, 1, 3, 3).repeat(self.c_in, 1, 1, 1) # [c_in, 1, 3, 3]
            z = F.pad(z, [1, 1, 1, 1], self.padding_mode)
            return F.conv2d(z, conv_weights, groups=self.c_in)
        
        def _perceive_with_torch_5x5(z, weight):
            conv_weights = weight.reshape(1, 1, 5, 5).repeat(self.c_in, 1, 1, 1) # [c_in, 1, 5, 5]
            z = F.pad(z, [2, 2, 2, 2], self.padding_mode)
            return F.conv2d(z, conv_weights, groups=self.c_in)
        
        def _perceive_with_torch_7x7(z, weight):
            conv_weights = weight.reshape(1, 1, 7, 7).repeat(self.c_in, 1, 1, 1) # [c_in, 1, 7, 7]
            z = F.pad(z, [3, 3, 3, 3], self.padding_mode)
            return F.conv2d(z, conv_weights, groups=self.c_in)

        y1 = _perceive_with_torch(x, self.sobel_filter_x)
        y2 = _perceive_with_torch(x, self.sobel_filter_y)
        y3 = _perceive_with_torch(x, self.laplacian_filter)
        # y4 = _perceive_with_torch(x, self.learnable_filter_3x3)
        # y4 = _perceive_with_torch_5x5(x, self.learnable_filter_5x5)
        # y4 = _perceive_with_torch_7x7(x, self.learnable_filter_7x7)

        tensor_list = [x]
        tensor_list += [y1, y2, y3]
        y = torch.cat(tensor_list, dim=1)

        # # channel attention
        # y = y * self.channel_attention(y)
        # # spatial attention
        # # y = y * self.spatial_attention(y)
        # avg_out = torch.mean(y, dim=1, keepdim=True) 
        # max_out, _ = torch.max(y, dim=1, keepdim=True)
        # y = y * self.spatial_attention(torch.cat([avg_out, max_out], dim=1))
        # import pdb; pdb.set_trace()

        if scale != 0:
            y = F.interpolate(y, size=(h, w), mode='bilinear', align_corners=False)

        return y

    def perceive_multiscale(self, x, pos_emb_mat=None):
        perceptions = []
        y = 0
        for scale in self.perception_scales:
            z = self.perceive_torch(x, scale=scale)
            perceptions.append(z)
        y = sum(perceptions)
        y = y / len(self.perception_scales)

        if pos_emb_mat is not None:
            y = torch.cat([y, pos_emb_mat], dim=1)

        return y

    def to_rgb(self, x):
        return x[:, :self.c_out, ...] * 0.25

    def seed(self, n, h=128, w=128):
        z = self.p_z.sample((n, h, w)).reshape(n, self.c_in, h, w) * 0.25
        return z

    def forward_nsteps(self, input_state, step_n, update_rate=0.5, return_middle_feature=False):
        nca_state = input_state
        middle_feature_list = []
        for _ in range(step_n):
            nca_state, nca_feature = self(nca_state, update_rate=update_rate)
            if return_middle_feature:
                middle_feature_list.append(nca_feature)
        if return_middle_feature:
            return nca_state, nca_feature, middle_feature_list
        return nca_state, nca_feature
    
    def forward(self, x, update_rate=0.5, return_perception=False):
        if self.pos_emb_2d:
            y_percept = self.perceive_multiscale(x, pos_emb_mat=self.pos_emb_2d(x))
        else:
            y_percept = self.perceive_multiscale(x)
        y = self.update_net(y_percept)
        # y = self.w2(F.relu(self.w1(y_percept)))
        b, c, h, w = y.shape

        update_mask = (torch.rand(b, 1, h, w, device=self.device) + update_rate).floor()
        # import pdb; pdb.set_trace()

        x = x + y * update_mask

        if return_perception:
            return x, self.to_rgb(x), y_percept
        else:
            return x, self.to_rgb(x)
        
    def get_embedding(self, model, image_path, obs_index, text_embeddings, img_embeddings):
        x = model.seed(1)
        target_texture_name = image_path.split('/')[-1]
        # import pdb; pdb.set_trace()
        real_idx = np.where(obs_index == target_texture_name)[0]
        # loaded_embeddings = np.load('text_embeddings_pca.npy')
        text_embedding = torch.from_numpy(text_embeddings[real_idx]).float()
        text_embedding = text_embedding.reshape(1, 20, 1, 1).expand(1, -1, 128, 128) 
        x[:, -20:, :, :] = text_embedding
        img_embedding = torch.from_numpy(img_embeddings[real_idx]).float()
        img_embedding = img_embedding.reshape(1, 20, 1, 1).expand(1, -1, 128, 128) 
        x[:, -40:-20, :, :] = img_embedding
        return x

if __name__ == "__main__":
    device = torch.device("cuda")
    model = DyNCA(c_in=12, c_out=3, fc_dim=96, device=device)

    state_dict = torch.load("weights.pt", map_location="cpu")
    model.load_state_dict(state_dict)

    from tqdm import tqdm
    from utils.video_utils import VideoWriter

    with VideoWriter() as vid, torch.no_grad():
        s = model.seed(1, 512, 512).to(device)
        for k in tqdm(range(600)):
            step_n = 8
            for i in range(step_n):
                s, _ = model(s)

            img = model.to_rgb(s[0]).permute(1, 2, 0).cpu()
            vid.add(img)