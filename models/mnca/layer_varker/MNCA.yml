experiment_name: "MNCA"
description: "Training MNCA model on all images in data images folder."
device: "cuda:0"


loss:
  attr:
    loss_type: "OT" #'OT', 'SlW', 'Gram'


model:
  type: "MNCA"
  attr:
    c_in: 30
    c_out: 3
    n_layers: 3
    embed_dim: 20
    fc_dim: 96
    padding_mode: 'replicate'
    pos_emb: 'CPE' # 'None' CPE
    perception_scales: [0,1,2,3,4,5]
  motion:
    motion_model: 'two_stream_dynamic'
    motion_vector_field_name: 'circular' # grad, circular, circle, diverge, converge, hyperbolic, 2block_x, 2block_y, 3block, 4block
    motion_img_size: [128, 128]
    motion_strength_weight: 15.0
    motion_direction_weight: 10.0
    nca_base_num_steps: 24
  motion_video:
    motion_model: 'two_stream_dynamic'
    video_motion_loss_type: 'MotionOT' # MotionOT, MotionSlW, MotionGram
    motion_img_size: [128, 128]
    img_size: [128, 128]
    


training:
  device: "cuda:0"
  lr: 0.001
  batch_size: 4
  iterations: 4000
  overflow_weight: 10.0
  log_interval: 100 # 250

  scheduler:
    type: "MultiStep"
    attr:
      milestones: [ 1000, 2000 ]
      gamma: 0.5

  nca:
    pool_size: 256
    step_range: [ 32, 128 ]
    inject_seed_step: 10

