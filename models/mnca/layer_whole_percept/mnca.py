import os
import shutil
import argparse
import yaml
from tqdm import tqdm
import torch
import numpy as np
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from models.mnca.layer_whole_percept.model import  DyNCA
# from models.mnca.utils.dataset_spot import SpotDataset

def imread(url, max_size=None, mode=None):
    if url.startswith(('http:', 'https:')):
        # wikimedia requires a user agent
        headers = {
            "User-Agent": "Requests in Colab/0.0 (https://colab.research.google.com/; <EMAIL>) requests/0.0"
        }
        r = requests.get(url, headers=headers)
        f = io.BytesIO(r.content)
    else:
        f = url
    img = Image.open(f)
    if max_size is not None:
        img.thumbnail((max_size, max_size), Image.LANCZOS)  # preserves aspect ratio
    if mode is not None:
        img = img.convert(mode)
    img = np.float32(img) / 255.0
    return img




class MNCA(torch.nn.Module):
    def __init__(self, config_path, device=None, image_path=None):
        super().__init__()
        self.model_name = "MNCA-whole-percept"
        self.epoch = 0
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        with open(config_path, 'r') as stream:
            self.config = yaml.load(stream, Loader=yaml.FullLoader)
        self.image_path = image_path

        # Initialize model parameters from config
        self.batch_size = 1

        # Initialize model
        self.model = DyNCA(**self.config['model']['attr']).to(device)

        # dataset = SpotDataset('control_P1', 'MISC13', 20, sample_size=1000)
        self.text_embeddings = np.load('models/mnca/layer_whole/pca/pca_text_embeddings.npy', allow_pickle=True)
        self.img_embeddings = np.load('models/mnca/layer_whole/pca/pca_img_embeddings.npy', allow_pickle=True)
        self.obs_index = np.load('models/mnca/layer_whole/pca/obs_index.npy', allow_pickle=True)

        self.state = self.model.get_embedding(self.model, self.image_path, self.obs_index, self.text_embeddings, self.img_embeddings)


    def reset(self):
        """Reset the model state"""
        self.state = self.model.get_embedding(self.model, self.image_path, self.obs_index, self.text_embeddings, self.img_embeddings)
        self.epoch = 0
        return self.state


    def step(self, steps=1):
        """Take steps in the model"""
        # time_step = self.epoch
        # if time_step > 24:
        #     time_step = 24
        with torch.no_grad():
            for _ in range(steps):
                state, _ = self.model.forward_nsteps(self.state, step_n=1, update_rate=0.5)
                self.state = state
                # print(self.state)
                self.epoch += 1

    def get_image(self):
        """Get the current image as a numpy array"""
        with torch.no_grad():
            rgb_state = self.model.to_rgb(self.state.clone())
            img = rgb_state[0].permute(1, 2, 0).detach().cpu().numpy()
            img = ((np.clip(img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

            return img


