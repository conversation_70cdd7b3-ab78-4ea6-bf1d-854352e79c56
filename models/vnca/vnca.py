import os
import shutil
import argparse
import yaml
from tqdm import tqdm
import torch
import numpy as np
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from models.vnca.model import  NCA

def imread(url, max_size=None, mode=None):
    if url.startswith(('http:', 'https:')):
        # wikimedia requires a user agent
        headers = {
            "User-Agent": "Requests in Colab/0.0 (https://colab.research.google.com/; <EMAIL>) requests/0.0"
        }
        r = requests.get(url, headers=headers)
        f = io.BytesIO(r.content)
    else:
        f = url
    img = Image.open(f)
    if max_size is not None:
        img.thumbnail((max_size, max_size), Image.LANCZOS)  # preserves aspect ratio
    if mode is not None:
        img = img.convert(mode)
    img = np.float32(img) / 255.0
    return img


class TextureDataset(Dataset):
    def __init__(self, data_dir, max_size=128):
        self.image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(max_size),
            transforms.ToTensor(), # C H W, i.e., permute(2, 0, 1)
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img = imread(self.image_paths[idx], max_size=None)  # Resize is handled in transform
        img = self.transform(img)
        random_channels = (torch.rand(9, 128, 128) - 0.5) * 0.25
        img = torch.cat([img, random_channels], dim=0)
        texture_name = os.path.splitext(os.path.basename(self.image_paths[idx]))[0]
        return img, texture_name
    
    
class SingleImageDataset(TextureDataset):
    def __init__(self, image_path, max_size=128):
        super().__init__('data', max_size=max_size)
        # Override image_paths with just our single image
        if os.path.isfile(image_path):
            self.image_paths = [image_path]
        else:
            image_files = [os.path.join(image_path, f) for f in os.listdir(image_path)
                        if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            if image_files:
                self.image_paths = [image_files[0]]
            else:
                raise ValueError(f"No image files found in {image_path}")
    def __len__(self):
        return 1
    

class VNCA(torch.nn.Module):
    def __init__(self, config_path, device=None, image_path=None):
        super().__init__()
        self.model_name = "VNCA"
        self.state = None
        self.epoch = 0
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        with open(config_path, 'r') as stream:
            self.config = yaml.load(stream, Loader=yaml.FullLoader)
        self.chn = self.config['model']['attr']['chn']
        self.image_path = image_path

        # Initialize model parameters from config
        self.batch_size = 1
        self.step_range = self.config['training']['nca']['step_range']
        self.inject_seed_step = self.config['training']['nca']['inject_seed_step']
        self.pool_size = self.config['training']['nca']['pool_size']

        # Initialize model
        self.model = NCA(**self.config['model']['attr']).to(device)

        # Use the provided image path or default to a directory
        if image_path is None:
            image_path = 'data'
        
        dataset = SingleImageDataset(image_path, max_size=128)
        dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
        
        # Get the first (and only) batch
        for batch_idx, (targets, texture_names) in enumerate(dataloader):
            self.original_x = targets.to(device)
            break  # Only process the first batch
        
        self.state = self.reset()


    def reset(self):
        """Reset the model state"""
        dataset = SingleImageDataset(self.image_path, max_size=128)
        dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
        for batch_idx, (targets, texture_names) in enumerate(dataloader):
            self.original_x = targets.to(self.device)
            break 
        with torch.no_grad():
            self.state, _, _ = self.model(self.original_x)
            self.epoch = 0
        return self.state

    def step(self, steps=1):
        """Take steps in the model"""
        # time_step = self.epoch
        # if time_step > 24:
        #     time_step = 24

        with torch.no_grad():
            for _ in range(steps):
                states = self.model.decode(self.state)
                self.state = states[-1]
                self.epoch += 1

    def get_image(self):
        """Get the current image as a numpy array"""
        with torch.no_grad():

            rgb_state = self.model.to_rgb(self.state.clone())
            img = rgb_state[0].permute(1, 2, 0).detach().cpu().numpy()
            img = ((np.clip(img, -1, 1)+1)/2.0 * 255.0).astype(np.uint8)

            return img


