import os
import torch

from nca_ui import NCAUI, load_target_image
# from nca_ui_txloss import NCAUI, load_target_image

from models.nca_internal.nca_internal import NCA_internal
from models.noise_nca.noise_nca import NoiseNCA
from models.mvnca.mvnca import MVNC<PERSON>
from models.vnca.vnca import VNC<PERSON>
from models.dynca_internal.dynca_internal import DyNCA_internal
# from models.mnca.layer_ori.mnca import MNCA
# from models.mnca.layer_varker.mnca import MNCA
# from models.mnca.layer_deep.mnca import MNCA
# from models.mnca.layer_whole.mnca import MNCA
from models.mnca.layer_whole_keep.mnca import MNCA


image_folder_name = 'data/image_whole'
data_dir = 'data/image_whole/GT_IZ_P15_CAACTATATCGAATGC-1_fullres.png'
# image_folder_name = 'images'
# data_dir = 'data/images/MUS-TCGA-CISWLWKL.jpg'

config_noise_nca='models/noise_nca/Noise-NCA.yml'
noise_nca_weights = 'models/noise_nca/final_model.pth'

config_mvnca='models/mvnca/MVNCA.yml'
mvnca_weights = 'models/mvnca/final_model_epoch150.pth'

config_vnca = 'models/vnca/VNCA.yml'
vnca_weights = 'models/vnca/final_model_epoch20.pth'

config_nca_internal = 'models/nca_internal/Noise-NCA.yml'
nca_internal_weights = 'models/nca_internal/final_model_2block_x.pth'
# nca_internal_weights = 'models/nca_internal/final_model_circular.pth'
# nca_internal_weights = 'models/nca_internal/final_model_mask.pth'

config_dynca_internal = 'models/dynca_internal/DyNCA.yml'
dynca_internal_weights = 'models/dynca_internal/final_model_grad_0_180.pth'

# config_mnca = 'models/mnca/layer_ori/MNCA.yml'
# mnca_weights = 'models/mnca/layer_ori/final_model.pth'
# config_mnca = 'models/mnca/layer_varker/MNCA.yml'
# mnca_weights = 'models/mnca/layer_varker/final_model.pth'
# config_mnca = 'models/mnca/layer_deep/MNCA.yml'
# mnca_weights = 'models/mnca/layer_deep/final_model.pth'
# config_mnca = 'models/mnca/layer_whole/MNCA.yml'
# mnca_weights = 'models/mnca/layer_whole/final_model_epoch800.pth'
config_mnca = 'models/mnca/layer_whole_keep/MNCA.yml'
mnca_weights = 'models/mnca/layer_whole_keep/final_model_epoch300.pth'


def main():
    device = torch.device('cpu')

    # Initialize models
    noise_nca = NoiseNCA(config_path=config_noise_nca, device=device)
    state_dict = torch.load(noise_nca_weights, map_location=device)
    noise_nca.model.load_state_dict(state_dict, strict=False)
    # Set initial target image for NoiseNCA
    noise_nca.target_image = load_target_image(data_dir)
    
    # mvnca = MVNCA(config_path=config_mvnca, device=device, image_path=data_dir)
    # state_dict = torch.load(mvnca_weights, map_location=device)
    # mvnca.model.load_state_dict(state_dict, strict=False)

    vnca = VNCA(config_path=config_vnca, device=device, image_path=data_dir)
    state_dict = torch.load(vnca_weights, map_location=device)
    vnca.model.load_state_dict(state_dict, strict=False)

    nca_internal = NCA_internal(config_path=config_nca_internal, device=device, image_path=data_dir)
    state_dict = torch.load(nca_internal_weights, map_location=device)
    nca_internal.model.load_state_dict(state_dict, strict=False)

    dynca_internal = DyNCA_internal(config_path=config_dynca_internal, device=device, image_path=data_dir)
    state_dict = torch.load(dynca_internal_weights, map_location=device)
    dynca_internal.model.load_state_dict(state_dict, strict=False)

    mnca = MNCA(config_path=config_mnca, device=device, image_path=data_dir)
    state_dict = torch.load(mnca_weights, map_location=device)
    mnca.model.load_state_dict(state_dict, strict=False)


    print("Models initialized successfully")

    # Start with MVNCA model
    current_model = mnca
    current_image_path = data_dir

    # Run the UI with model switching
    while True:
        ui = NCAUI(current_model, current_image_path, image_folder_name)
        result = ui.run()
        if result == "switch_model":
            current_image_path = ui.test_image_path
            # if current_model == noise_nca:
            #     mvnca.image_path = current_image_path
            #     mvnca.reset()
            #     current_model = mvnca
            if current_model == noise_nca:
                vnca.image_path = current_image_path
                vnca.reset()
                current_model = vnca
            elif current_model == vnca:
                nca_internal.image_path = current_image_path
                nca_internal.reset()
                current_model = nca_internal
            elif current_model == nca_internal:
                mnca.image_path = current_image_path
                mnca.reset()
                current_model = mnca
            elif current_model == mnca:
                dynca_internal.image_path = current_image_path
                dynca_internal.reset()
                current_model = dynca_internal
            elif current_model == dynca_internal:
                noise_nca.image_path = current_image_path
                noise_nca.reset()
                current_model = noise_nca
            print(f"Switched to {current_model.model_name}")

        elif result == "reload_image":
            current_image_path = ui.test_image_path
            print(f"Loading new image: {os.path.basename(current_image_path)}")
            if current_model.model_name == "MNCA":
                mnca.image_path = current_image_path
                mnca.reset()
                current_model = mnca
                print(f"MNCA model reset with epoch {current_model.epoch}")
            # elif current_model.model_name == "MVNCA":
            #     mvnca.image_path = current_image_path
            #     mvnca.reset()
            #     current_model = mvnca
            #     print(f"MVNCA model reinitialized with epoch {current_model.epoch}")
            elif current_model.model_name == "NoiseNCA":
                noise_nca.target_image = load_target_image(current_image_path)
                noise_nca.reset()
                current_model = noise_nca
                print(f"NoiseNCA model reset with epoch {current_model.epoch}")
            elif current_model.model_name == "VNCA":
                vnca.image_path = current_image_path
                vnca.reset()
                current_model = vnca
                print(f"VNCA model reset with epoch {current_model.epoch}")
            elif current_model.model_name == "NCA_internal":
                nca_internal.image_path = current_image_path
                nca_internal.reset()
                current_model = nca_internal
                print(f"NCA_internal model reset with epoch {current_model.epoch}")
            elif current_model.model_name == "DyNCA_internal":
                dynca_internal.image_path = current_image_path
                dynca_internal.reset()
                current_model = dynca_internal
                print(f"DyNCA_internal model reset with epoch {current_model.epoch}")
        elif result is None:
            break


if __name__ == "__main__":
    main()
